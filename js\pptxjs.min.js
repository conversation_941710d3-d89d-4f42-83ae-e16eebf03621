
/**
 * pptxjs.js
 * Ver. : 1.21.1
 * last update: 16/11/2021
 * Author: meshesha , https://github.com/meshesha
 * LICENSE: MIT
 * url:https://pptx.js.org/
 */
!function (ta) { var t; ta.fn.pptxToHtml = function (t) { var h, d = ta(this), p = d.attr("id"), c = !1, g = new Array, T = null, b = 0, e = 1, w = ["he-IL", "ar-AE", "ar-SA", "dv-MV", "fa-IR", "ur-PK"], tl = 96 / 914400, R = 1.25, f = 0, u = 0, v = !1, A = !0, rl = {}, D = ta.extend(!0, { pptxFileUrl: "", fileInputId: "", slidesScale: "", slideMode: !1, slideType: "divs2slidesjs", revealjsPath: "", keyBoardShortCut: !1, mediaProcess: !0, jsZipV2: !1, themeProcess: !0, incSlide: { width: 0, height: 0 }, slideModeConfig: { first: 1, nav: !0, navTxtColor: "black", keyBoardShortCut: !0, showSlideNum: !0, showTotalSlideNum: !0, autoSlide: !0, randomAutoSlide: !1, loop: !1, background: !1, transition: "default", transitionTime: 1 }, revealjsConfig: {} }, t), A = D.themeProcess; if (ta("#" + p).prepend(ta("<div></div>").attr({ class: "slides-loadnig-msg", style: "display:block; width:100%; color:white; background-color: #ddd;" }).html(ta("<div></div>").attr({ class: "slides-loading-progress-bar", style: "width: 1%; background-color: #4775d1;" }).html("<span style='text-align: center;'>Loading... (1%)</span>"))), D.slideMode && (jQuery().divs2slides || jQuery.getScript("./js/divs2slides.js")), !1 !== D.jsZipV2 && (jQuery.getScript(D.jsZipV2), "yes" !== localStorage.getItem("isPPTXjsReLoaded") && (localStorage.setItem("isPPTXjsReLoaded", "yes"), location.reload())), D.keyBoardShortCut && ta(document).bind("keydown", function (a) { a.preventDefault(); a = a.keyCode; console.log(a, c), 116 != a || v || (v = !0, L(p, D)) }), FileReaderJS.setSync(!1), "" != D.pptxFileUrl) try { JSZipUtils.getBinaryContent(D.pptxFileUrl, function (a, t) { var r = new Blob([t]), t = D.pptxFileUrl.split("."); t.pop(), r.name = t[0], FileReaderJS.setupBlob(r, { readAsDefault: "ArrayBuffer", on: { load: function (a, t) { s(a.target.result) } } }) }) } catch (a) { console.error("file url error (" + D.pptxFileUrl + "0)"), ta(".slides-loadnig-msg").remove() } else ta(".slides-loadnig-msg").remove(); function s(a) { if (a.byteLength < 10) return console.error("file url error (" + D.pptxFileUrl + "0)"), void ta(".slides-loadnig-msg").remove(); for (var t, r, e = function (a) { var t = [], r = new Date; null !== a.file("docProps/thumbnail.jpeg") && (c = J(a.file("docProps/thumbnail.jpeg").asArrayBuffer()), t.push({ type: "pptx-thumb", data: c, slide_num: -1 })); var e = function (a) { for (var t = G(a, "[Content_Types].xml").Types.Override, r = [], e = [], s = 0; s < t.length; s++)switch (t[s].attrs.ContentType) { case "application/vnd.openxmlformats-officedocument.presentationml.slide+xml": r.push(t[s].attrs.PartName.substr(1)); break; case "application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml": e.push(t[s].attrs.PartName.substr(1)) }return { slides: r, slideLayouts: e } }(a), s = function (a) { var t = G(a, "docProps/app.xml").Properties.AppVersion; h = parseInt(t), console.log("create by Office PowerPoint app verssion: ", t); var r = G(a, "ppt/presentation.xml"), e = r["p:presentation"]["p:sldSz"].attrs, t = parseInt(e.cx), a = parseInt(e.cy), e = e.type; return console.log("Presentation size type: ", e), T = r["p:presentation"]["p:defaultTextStyle"], f = t * tl + D.incSlide.width | 0, u = a * tl + D.incSlide.height | 0, { width: f, height: u } }(a); tableStyles = G(a, "ppt/tableStyles.xml"), t.push({ type: "slideSize", data: s, slide_num: 0 }); for (var o = e.slides.length, i = 0; i < o; i++) { var n = e.slides[i], l = ""; l = -1 != n.indexOf("/") ? n.split("/").pop() : n; var d = ""; -1 != l.indexOf(".") && (p = l.split("."), p.pop(), d = p.join(".")); var p = 1; "" != d && -1 != l.indexOf("slide") && (p = Number(d.substr(5))); n = function (a, t, r, e) { var s = t.replace("slides/slide", "slides/_rels/slide") + ".rels", o = G(a, s).Relationships.Relationship, i = "", n = "", l = {}; if (o.constructor === Array) for (var d = 0; d < o.length; d++)switch (o[d].attrs.Type) { case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout": i = o[d].attrs.Target.replace("../", "ppt/"); break; case "http://schemas.microsoft.com/office/2007/relationships/diagramDrawing": n = o[d].attrs.Target.replace("../", "ppt/"), l[o[d].attrs.Id] = { type: o[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: o[d].attrs.Target.replace("../", "ppt/") }; break; case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesSlide": case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/image": case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart": case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink": default: l[o[d].attrs.Id] = { type: o[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: o[d].attrs.Target.replace("../", "ppt/") } } else i = o.attrs.Target.replace("../", "ppt/"); var p = G(a, i), c = j(p), h = ul(p, ["p:sldLayout", "p:clrMapOvr", "a:overrideClrMapping"]); void 0 !== h && (slideLayoutClrOvride = h.attrs); var f = i.replace("slideLayouts/slideLayout", "slideLayouts/_rels/slideLayout") + ".rels", u = G(a, f); o = u.Relationships.Relationship; var v = "", L = {}; if (o.constructor === Array) for (d = 0; d < o.length; d++)"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" === o[d].attrs.Type ? v = o[d].attrs.Target.replace("../", "ppt/") : L[o[d].attrs.Id] = { type: o[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: o[d].attrs.Target.replace("../", "ppt/") }; else v = o.attrs.Target.replace("../", "ppt/"); s = G(a, v), h = ul(s, ["p:sldMaster", "p:txStyles"]), f = j(s), u = v.replace("slideMasters/slideMaster", "slideMasters/_rels/slideMaster") + ".rels", u = G(a, u); o = u.Relationships.Relationship; var g = "", b = {}; if (o.constructor === Array) for (d = 0; d < o.length; d++)"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" === o[d].attrs.Type ? g = o[d].attrs.Target.replace("../", "ppt/") : b[o[d].attrs.Id] = { type: o[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: o[d].attrs.Target.replace("../", "ppt/") }; else g = o.attrs.Target.replace("../", "ppt/"); var m = {}; if (void 0 !== g) { var y = g.split("/").pop(), k = g.replace(y, "_rels/" + y) + ".rels", M = G(a, g), x = G(a, k); if (null !== x) if (void 0 !== (w = x.Relationships.Relationship)) { g = ""; if (w.constructor === Array) for (d = 0; d < w.length; d++)m[w[d].attrs.Id] = { type: w[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: w[d].attrs.Target.replace("../", "ppt/") }; else m[w.attrs.Id] = { type: w.attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: w.attrs.Target.replace("../", "ppt/") } } } var P = {}, y = {}; if (void 0 !== n) { k = n.split("/").pop(), x = n.replace(k, "_rels/" + k) + ".rels"; null != (y = G(a, n)) && "" != y && (k = (k = JSON.stringify(y)).replace(/dsp:/g, "p:"), y = JSON.parse(k)); x = G(a, x); if (null !== x) { var w = x.Relationships.Relationship, g = ""; if (w.constructor === Array) for (d = 0; d < w.length; d++)P[w[d].attrs.Id] = { type: w[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: w[d].attrs.Target.replace("../", "ppt/") }; else P[w.attrs.Id] = { type: w.attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: w.attrs.Target.replace("../", "ppt/") } } } var t = G(a, t, !0), I = t["p:sld"]["p:cSld"]["p:spTree"], _ = { zip: a, slideLayoutContent: p, slideLayoutTables: c, slideMasterContent: s, slideMasterTables: f, slideContent: t, slideResObj: l, slideMasterTextStyles: h, layoutResObj: L, masterResObj: b, themeContent: M, themeResObj: m, digramFileContent: y, diagramResObj: P, defaultTextStyle: T }, M = ""; !0 === A && (M = function (a, t, r) { a.slideContent; var e = a.slideLayoutContent, s = a.slideMasterContent, o = ul(e, ["p:sldLayout", "p:cSld", "p:spTree"]), i = ul(s, ["p:sldMaster", "p:cSld", "p:spTree"]), s = ul(e, ["p:sldLayout", "attrs", "showMasterSp"]), e = O(a, r), n = "<div class='slide-background-" + r + "' style='width:" + t.width + "px; height:" + t.height + "px;" + e + "'>"; if (void 0 !== o) for (var l in o) if (o[l].constructor === Array) for (var d = 0; d < o[l].length; d++)"pic" != ul(o[l][d], ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"]) && (n += z(l, o[l][d], o, a, "slideLayoutBg")); else "pic" != ul(o[l], ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"]) && (n += z(l, o[l], o, a, "slideLayoutBg")); if (void 0 !== i && ("1" == s || void 0 === s)) for (var l in i) if (i[l].constructor === Array) for (d = 0; d < i[l].length; d++) { ul(i[l][d], ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"]); n += z(l, i[l][d], i, a, "slideMasterBg") } else { ul(i[l], ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"]); n += z(l, i[l], i, a, "slideMasterBg") } return n }(_, e, r)); var C, y = ""; "colorsAndImageOnly" == A && (y = O(_, r)); { var S; S = D.slideMode && "revealjs" == D.slideType ? "<section class='slide' style='width:" + e.width + "px; height:" + e.height + "px;" + y + "'>" : "<div class='slide' style='width:" + e.width + "px; height:" + e.height + "px;" + y + "'>" } for (C in S += M, I) if (I[C].constructor === Array) for (d = 0; d < I[C].length; d++)S += z(C, I[C][d], I, _, "slide"); else S += z(C, I[C], I, _, "slide"); return D.slideMode && "revealjs" == D.slideType ? S + "</div></section>" : S + "</div></div>" }(a, n, i, s); t.push({ type: "slide", data: n, slide_num: p, file_name: d }), t.push({ type: "progress-update", slide_num: o + i + 1, data: 100 * (i + 1) / o }) } t.sort(function (a, t) { return a.slide_num - t.slide_num }), t.push({ type: "globalCSS", data: function () { var a, t = ""; for (a in rl) t += " ." + rl[a].name + (rl[a].suffix || "") + "{" + rl[a].text + "}\n"; D.slideMode && "divs2slidesjs" == D.slideType && (t += "#all_slides_warpper{margin-right: auto;margin-left: auto;padding-top:10px;width: " + f + "px;}\n"); return t }() }); var c = new Date; return t.push({ type: "ExecutionTime", data: c - r }), t }((new JSZip).load(a)), s = 0; s < e.length; s++)switch (e[s].type) { case "slide": d.append(e[s].data); break; case "pptx-thumb": break; case "slideSize": f = e[s].data.width, u = e[s].data.height; break; case "globalCSS": d.append("<style>" + e[s].data + "</style>"); break; case "ExecutionTime": !function (a) { for (var t = 0; t < a.length; t++)!function (a) { var t = a.chartID, r = a.chartType, e = a.chartData, s = [], o = null; switch (r) { case "lineChart": s = e, (o = nv.models.lineChart().useInteractiveGuideline(!0)).xAxis.tickFormat(function (a) { return e[0].xlabels[a] || a }); break; case "barChart": s = e, (o = nv.models.multiBarChart()).xAxis.tickFormat(function (a) { return e[0].xlabels[a] || a }); break; case "pieChart": case "pie3DChart": 0 < e.length && (s = e[0].values), o = nv.models.pieChart(); break; case "areaChart": s = e, (o = nv.models.stackedAreaChart().clipEdge(!0).useInteractiveGuideline(!0)).xAxis.tickFormat(function (a) { return e[0].xlabels[a] || a }); break; case "scatterChart": for (var i = 0; i < e.length; i++) { for (var n = [], l = 0; l < e[i].length; l++)n.push({ x: l, y: e[i][l] }); s.push({ key: "data" + (i + 1), values: n }) } (o = nv.models.scatterChart().showDistX(!0).showDistY(!0).color(d3.scale.category10().range())).xAxis.axisLabel("X").tickFormat(d3.format(".02f")), o.yAxis.axisLabel("Y").tickFormat(d3.format(".02f")) }null !== o && (d3.select("#" + t).append("svg").datum(s).transition().duration(500).call(o), nv.utils.windowResize(o.update), c = !0) }(a[t].data) }(g), Y(ta(".block")), Y(ta("table td")), c = !0, D.slideMode && !v ? (v = !0, L(p, D)) : D.slideMode || ta(".slides-loadnig-msg").remove(); break; case "progress-update": t = e[s].data, r = void 0, (r = ta(".slides-loading-progress-bar")).width(t + "%"), r.html("<span style='text-align: center;'>Loading...(" + t + "%)</span>") }(!D.slideMode || D.slideMode && "revealjs" == D.slideType) && (null === document.getElementById("all_slides_warpper") && ta("#" + p + " .slide").wrapAll("<div id='all_slides_warpper' class='slides'></div>"), D.slideMode && "revealjs" == D.slideType && ta("#" + p).addClass("reveal")); var o = D.slidesScale, i = ""; "" != o && (l = parseInt(o) / 100, D.slideMode && "revealjs" != D.slideType && (i = "transform:scale(" + l + "); transform-origin:top")); var n = ta("#" + p + " .slide").height(), a = ta("#" + p + " .slide").length, l = "" != o ? l : 1; ta("#all_slides_warpper").attr({ style: i + ";height: " + a * n * l + "px" }) } function L(e, s) { var o, a; "" == s.slideType || "divs2slidesjs" == s.slideType ? (o = ta("#" + e + " .slide").height(), ta("#" + e + " .slide").hide(), setTimeout(function () { var a = s.slideModeConfig; ta(".slides-loadnig-msg").remove(), ta("#" + e).divs2slides({ first: a.first, nav: a.nav, showPlayPauseBtn: s.showPlayPauseBtn, navTxtColor: a.navTxtColor, keyBoardShortCut: a.keyBoardShortCut, showSlideNum: a.showSlideNum, showTotalSlideNum: a.showTotalSlideNum, autoSlide: a.autoSlide, randomAutoSlide: a.randomAutoSlide, loop: a.loop, background: a.background, transition: a.transition, transitionTime: a.transitionTime }); var t = s.slidesScale, a = ""; "" != t && (a = "transform:scale(" + (r = parseInt(t) / 100) + "); transform-origin:top"); var r = "" != t ? r : 1; ta("#all_slides_warpper").attr({ style: a + ";height: " + +o * r + "px" }) }, 1500)) : "revealjs" == s.slideType && (ta(".slides-loadnig-msg").remove(), a = (a = "") != s.revealjsPath ? s.revealjsPath : "./revealjs/reveal.js", ta.getScript(a, function (a, t) { "success" == t && Reveal.initialize(s.revealjsConfig) })) } function G(a, t, r) { try { var e = a.file(t).asText(); r && h <= 12 && (e = e.replace(/<!\[CDATA\[(.*?)\]\]>/g, "$1")); e = aa(e, { simplify: 1 }); return void 0 !== e["?xml"] ? e["?xml"] : e } catch (a) { return null } } function j(a) { var t, r = a[Object.keys(a)[0]]["p:cSld"]["p:spTree"], e = {}, s = {}, o = {}; for (t in r) if ("p:nvGrpSpPr" != t && "p:grpSpPr" != t) { var i = r[t]; if (i.constructor === Array) for (var n = 0; n < i.length; n++) { var l = ul(c = i[n]["p:nvSpPr"], ["p:cNvPr", "attrs", "id"]), d = ul(c, ["p:nvPr", "p:ph", "attrs", "idx"]), p = ul(c, ["p:nvPr", "p:ph", "attrs", "type"]); void 0 !== l && (e[l] = i[n]), void 0 !== d && (s[d] = i[n]), void 0 !== p && (o[p] = i[n]) } else { var c, l = ul(c = i["p:nvSpPr"], ["p:cNvPr", "attrs", "id"]), d = ul(c, ["p:nvPr", "p:ph", "attrs", "idx"]), p = ul(c, ["p:nvPr", "p:ph", "attrs", "type"]); void 0 !== l && (e[l] = i), void 0 !== d && (s[d] = i), void 0 !== p && (o[p] = i) } } return { idTable: e, idxTable: s, typeTable: o } } function z(a, t, r, e, s, o) { var i, n, l, d, p, c, h, f, u, v, L = ""; switch (a) { case "p:sp": L = y(t, r, e, s, o); break; case "p:cxnSp": n = r, l = e, d = s, p = o, c = (i = t)["p:nvCxnSpPr"]["p:cNvPr"].attrs.id, h = i["p:nvCxnSpPr"]["p:cNvPr"].attrs.name, f = void 0 === i["p:nvCxnSpPr"]["p:nvPr"]["p:ph"] ? void 0 : i["p:nvSpPr"]["p:nvPr"]["p:ph"].attrs.idx, u = void 0 === i["p:nvCxnSpPr"]["p:nvPr"]["p:ph"] ? void 0 : i["p:nvSpPr"]["p:nvPr"]["p:ph"].attrs.type, v = i.attrs.order, L = k(i, n, void 0, void 0, c, h, f, u, v, l, void 0, p, d); break; case "p:pic": L = function (a, t, r) { var e = "", s = !1, o = a.attrs.order, i = a["p:blipFill"]["a:blip"].attrs["r:embed"]; y = "slideMasterBg" == r ? t.masterResObj : "slideLayoutBg" == r ? t.layoutResObj : t.slideResObj; var n = y[i].target, l = K(n).toLowerCase(), d = t.zip, p = d.file(n).asArrayBuffer(), c = "", h = a["p:spPr"]["a:xfrm"]; void 0 === h && (L = ul(a, ["p:nvPicPr", "p:nvPr", "p:ph", "attrs", "idx"]), ul(a, ["p:nvPicPr", "p:nvPr", "p:ph", "attrs", "type"]), void 0 !== L && (h = ul(t.slideLayoutTables, ["idxTable", L, "p:spPr", "a:xfrm"]))); var f = 0, r = ul(a, ["p:spPr", "a:xfrm", "attrs", "rot"]); void 0 !== r && (f = vl(r)); var u, v, i = ul(a, ["p:nvPicPr", "p:nvPr", "a:videoFile"]), n = !1, t = !1, L = D.mediaProcess; void 0 !== i & L && (r = i.attrs["r:link"], function (a) { return /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/.test(a) }(u = y[r].target) ? (u = Ll(u), s = n = t = !0) : "mp4" != (r = K(u).toLowerCase()) && "webm" != r && "ogg" != r || (b = d.file(u).asArrayBuffer(), m = X(r), k = new Blob([b], { type: m }), v = URL.createObjectURL(k), s = n = !0)); var g, b = ul(a, ["p:nvPicPr", "p:nvPr", "a:audioFile"]), m = !1; { var y, k, M; void 0 !== b & L && (k = b.attrs["r:link"], y = y[k].target, "mp3" != (k = K(y).toLowerCase()) && "wav" != k && "ogg" != k || (k = d.file(y).asArrayBuffer(), M = new Blob([k]), g = URL.createObjectURL(M), d = 20 * parseInt(h["a:ext"].attrs.cx), y = h["a:ext"].attrs.cy, k = parseInt(h["a:off"].attrs.x) / 2.5, M = h["a:off"].attrs.y, M = { "a:ext": { attrs: { cx: d, cy: y } }, "a:off": { attrs: { x: k, y: M } } }, s = n = m = !0)) } c = X(l), e = "<div class='block content' style='" + il(L && m ? M : h, a, void 0, void 0) + nl(L && m ? M : h, void 0, void 0) + " z-index: " + o + ";transform: rotate(" + f + "deg);'>", void 0 === i && void 0 === b || !L || !n ? e += "<img src='data:" + c + ";base64," + J(p) + "' style='width: 100%; height: 100%'/>" : (void 0 !== i || void 0 !== b) && L && n && (void 0 === i || t ? void 0 !== i && t && (e += "<iframe   src='" + u + "' controls style='width: 100%; height: 100%'></iframe >") : e += "<video  src='" + v + "' controls style='width: 100%; height: 100%'>Your browser does not support the video tag.</video>", void 0 !== b && (e += '<audio id="audio_player" controls ><source src="' + g + '"></audio>')); !n && s && (e += "<span style='color:red;font-size:40px;position: absolute;'>This media file Not supported by HTML5</span>"); void 0 === i && void 0 === b || L || !n || console.log("Founded supported media file but media process disabled (mediaProcess=false)"); return e += "</div>" }(t, e, s); break; case "p:graphicFrame": L = function (a, t, r, e) { var s = ""; switch (ul(a, ["a:graphic", "a:graphicData", "attrs", "uri"])) { case "http://schemas.openxmlformats.org/drawingml/2006/table": s = function (a, t) { var r = a.attrs.order, e = ul(a, ["a:graphic", "a:graphicData", "a:tbl"]), s = ul(a, ["p:xfrm"]), o = ul(a, ["a:graphic", "a:graphicData", "a:tbl", "a:tblPr"]), i = ul(a, ["a:graphic", "a:graphicData", "a:tbl", "a:tblGrid", "a:gridCol"]), n = ""; void 0 !== o && (u = o.attrs.rtl, n = 1 == u ? "dir=rtl" : "dir=ltr"); var l, d = o.attrs.firstRow, p = o.attrs.firstCol, c = o.attrs.lastRow, h = o.attrs.lastCol, f = o.attrs.bandRow, u = o.attrs.bandCol, v = { isFrstRowAttr: void 0 !== d && "1" == d ? 1 : 0, isFrstColAttr: void 0 !== p && "1" == p ? 1 : 0, isLstRowAttr: void 0 !== c && "1" == c ? 1 : 0, isLstColAttr: void 0 !== h && "1" == h ? 1 : 0, isBandRowAttr: void 0 !== f && "1" == f ? 1 : 0, isBandColAttr: void 0 !== u && "1" == u ? 1 : 0 }, L = o["a:tableStyleId"]; if (void 0 !== L) { var g = tableStyles["a:tblStyleLst"]["a:tblStyle"]; if (void 0 !== g) if (g.constructor === Array) for (var b = 0; b < g.length; b++)g[b].attrs.styleId == L && (l = g[b]); else g.attrs.styleId == L && (l = g) } void 0 !== l && (l.tblStylAttrObj = v, t.thisTbiStyle = l); f = ul(l, ["a:wholeTbl", "a:tcStyle"]), u = ul(f, ["a:tcBdr"]), o = ""; void 0 !== u && (o = Q(u, t)); f = "", u = ul(l, ["a:tblBg", "a:fillRef"]); void 0 !== u && (f = fl(u, void 0, void 0, t)); void 0 === u && (u = ul(l, ["a:wholeTbl", "a:tcStyle", "a:fill", "a:solidFill"]), f = fl(u, void 0, void 0, t)); "" !== f && (f = "background-color: #" + f + ";"); var m = "<table " + n + " style='border-collapse: collapse;" + il(s, a, void 0, void 0) + nl(s, void 0, void 0) + " z-index: " + r + ";" + o + ";" + f + "'>", y = e["a:tr"]; y.constructor !== Array && (y = [y]); for (var k, M = [], x = 0; x < y.length; x++) { var P = y[x].attrs.h, w = 0, I = ""; void 0 !== P && (w = parseInt(P) * tl, I += "height:" + w + "px;"); var _, C, S, T, A, D, G, j = "", R = "", P = "", w = ""; void 0 !== l && void 0 !== l["a:wholeTbl"] && (void 0 !== (_ = ul(l, ["a:wholeTbl", "a:tcStyle", "a:fill", "a:solidFill"])) && void 0 !== (C = fl(_, void 0, void 0, t)) && (j = C), void 0 !== (A = ul(l, ["a:wholeTbl", "a:tcTxStyle"])) && (void 0 !== (G = fl(A, void 0, void 0, t)) && (P = G), "" != (G = "on" == ul(A, ["attrs", "b"]) ? "bold" : "") && (w = G))), 0 == x && 1 == v.isFrstRowAttr && void 0 !== l ? (void 0 !== (_ = ul(l, ["a:firstRow", "a:tcStyle", "a:fill", "a:solidFill"])) && void 0 !== (C = fl(_, void 0, void 0, t)) && (j = C), void 0 !== (S = ul(l, ["a:firstRow", "a:tcStyle", "a:tcBdr"])) && "" != (T = Q(S, t)) && (R = T), void 0 !== (A = ul(l, ["a:firstRow", "a:tcTxStyle"])) && (void 0 !== (D = fl(A, void 0, void 0, t)) && (P = D), "" !== (G = "on" == ul(A, ["attrs", "b"]) ? "bold" : "") && (w = G))) : 0 < x && 1 == v.isBandRowAttr && void 0 !== l && (j = "", R = void 0, x % 2 == 0 && void 0 !== l["a:band2H"] && (void 0 !== (_ = ul(l, ["a:band2H", "a:tcStyle", "a:fill", "a:solidFill"])) && "" !== (C = fl(_, void 0, void 0, t)) && (j = C), void 0 !== (S = ul(l, ["a:band2H", "a:tcStyle", "a:tcBdr"])) && "" != (T = Q(S, t)) && (R = T), void 0 !== (A = ul(l, ["a:band2H", "a:tcTxStyle"])) && void 0 !== (D = fl(A, void 0, void 0, t)) && (P = D), "" !== (G = "on" == ul(A, ["attrs", "b"]) ? "bold" : "") && (w = G)), x % 2 != 0 && void 0 !== l["a:band1H"] && (void 0 !== (_ = ul(l, ["a:band1H", "a:tcStyle", "a:fill", "a:solidFill"])) && void 0 !== (C = fl(_, void 0, void 0, t)) && (j = C), void 0 !== (S = ul(l, ["a:band1H", "a:tcStyle", "a:tcBdr"])) && "" != (T = Q(S, t)) && (R = T), void 0 !== (A = ul(l, ["a:band1H", "a:tcTxStyle"])) && (void 0 !== (D = fl(A, void 0, void 0, t)) && (P = D), "" != (G = "on" == ul(A, ["attrs", "b"]) ? "bold" : "") && (w = G)))), x == y.length - 1 && 1 == v.isLstRowAttr && void 0 !== l && (void 0 !== (_ = ul(l, ["a:lastRow", "a:tcStyle", "a:fill", "a:solidFill"])) && void 0 !== (C = fl(_, void 0, void 0, t)) && (j = C), void 0 !== (S = ul(l, ["a:lastRow", "a:tcStyle", "a:tcBdr"])) && "" != (T = Q(S, t)) && (R = T), void 0 !== (A = ul(l, ["a:lastRow", "a:tcTxStyle"])) && (void 0 !== (D = fl(A, void 0, void 0, t)) && (P = D), "" !== (G = "on" == ul(A, ["attrs", "b"]) ? "bold" : "") && (w = G))), I += void 0 !== R ? R : "", I += void 0 !== P ? " color: #" + P + ";" : "", I += "" != w ? " font-weight:" + w + ";" : "", void 0 !== j && "" != j && (I += "background-color: #" + j + ";"), m += "<tr style='" + I + "'>"; var z = y[x]["a:tc"]; if (void 0 !== z) if (z.constructor === Array) { var B = 0; 0 == M.length && (M = Array.apply(null, Array(z.length)).map(function () { return 0 })); for (var F, N = 0; B < z.length;)0 == M[B] && 0 == N ? (0 == B && 1 == v.isFrstColAttr ? (k = "a:firstCol", 1 == v.isLstRowAttr && x == y.length - 1 && void 0 !== ul(l, ["a:seCell"]) ? k = "a:seCell" : 1 == v.isFrstRowAttr && 0 == x && void 0 !== ul(l, ["a:neCell"]) && (k = "a:neCell")) : !(0 < B && 1 == v.isBandColAttr) || 1 == v.isFrstColAttr && 0 == x || 1 == v.isLstRowAttr && x == y.length - 1 || B == z.length - 1 || B % 2 == 0 || void 0 === ul(l, ["a:band2V"]) && void 0 === ul(l, ["a:band1V"]) || (k = "a:band2V"), B == z.length - 1 && 1 == v.isLstColAttr && (k = "a:lastCol", 1 == v.isLstRowAttr && x == y.length - 1 && void 0 !== ul(l, ["a:swCell"]) ? k = "a:swCell" : 1 == v.isFrstRowAttr && 0 == x && void 0 !== ul(l, ["a:nwCell"]) && (k = "a:nwCell")), E = (O = V(z[B], i, 0, B, l, k, t))[0], H = O[1], q = O[2], U = O[3], F = O[4], void 0 !== U ? (M[B] = parseInt(U) - 1, m += "<td class='" + q + "' data-row='" + x + "," + B + "' rowspan ='" + parseInt(U) + "' style='" + H + "'>" + E + "</td>") : void 0 !== F ? (m += "<td class='" + q + "' data-row='" + x + "," + B + "' colspan = '" + parseInt(F) + "' style='" + H + "'>" + E + "</td>", N = parseInt(F) - 1) : m += "<td class='" + q + "' data-row='" + x + "," + B + "' style = '" + H + "'>" + E + "</td>") : (0 != M[B] && --M[B], 0 != N && N--), B++ } else { 1 == v.isFrstColAttr && 1 != v.isLstRowAttr ? k = "a:firstCol" : 1 != v.isBandColAttr || 1 == v.isLstRowAttr || void 0 === ul(l, ["a:band2V"]) && void 0 === ul(l, ["a:band1V"]) || (k = "a:band2V"), 1 == v.isLstColAttr && 1 != v.isLstRowAttr && (k = "a:lastCol"); var O, E = (O = V(z, i, 0, void 0, l, k, t))[0], H = O[1], q = O[2], U = O[3]; m += void 0 !== U ? "<td  class='" + q + "' rowspan='" + parseInt(U) + "' style = '" + H + "'>" + E + "</td>" : "<td class='" + q + "' style='" + H + "'>" + E + "</td>" } m += "</tr>" } return m }(a, t); break; case "http://schemas.openxmlformats.org/drawingml/2006/chart": s = function (a, t) { var r, e = a.attrs.order, s = ul(a, ["p:xfrm"]), e = "<div id='chart" + b + "' class='block content' style='" + il(s, a, void 0, void 0) + nl(s, void 0, void 0) + " z-index: " + e + ";'></div>", a = a["a:graphic"]["a:graphicData"]["c:chart"].attrs["r:id"], a = t.slideResObj[a].target, o = ul(G(t.zip, a), ["c:chartSpace", "c:chart", "c:plotArea"]), i = null; for (r in o) switch (r) { case "c:lineChart": i = { type: "createChart", data: { chartID: "chart" + b, chartType: "lineChart", chartData: U(o[r]["c:ser"]) } }; break; case "c:barChart": i = { type: "createChart", data: { chartID: "chart" + b, chartType: "barChart", chartData: U(o[r]["c:ser"]) } }; break; case "c:pieChart": i = { type: "createChart", data: { chartID: "chart" + b, chartType: "pieChart", chartData: U(o[r]["c:ser"]) } }; break; case "c:pie3DChart": i = { type: "createChart", data: { chartID: "chart" + b, chartType: "pie3DChart", chartData: U(o[r]["c:ser"]) } }; break; case "c:areaChart": i = { type: "createChart", data: { chartID: "chart" + b, chartType: "areaChart", chartData: U(o[r]["c:ser"]) } }; break; case "c:scatterChart": i = { type: "createChart", data: { chartID: "chart" + b, chartType: "scatterChart", chartData: U(o[r]["c:ser"]) } } }null !== i && g.push(i); return b++, e }(a, t); break; case "http://schemas.openxmlformats.org/drawingml/2006/diagram": s = function (a, t, r) { a.attrs.order; var e = t.zip, s = ul(a, ["p:xfrm"]), o = ul(a, ["a:graphic", "a:graphicData", "dgm:relIds", "attrs"]), i = o["r:cs"], n = o["r:dm"], l = o["r:lo"], o = o["r:qs"], i = t.slideResObj[i].target, n = t.slideResObj[n].target, l = t.slideResObj[l].target; dgmQuickStyleFileName = t.slideResObj[o].target; G(e, i), G(e, n), G(e, l), G(e, dgmQuickStyleFileName); var d = ul(t.digramFileContent, ["p:drawing", "p:spTree", "p:sp"]), p = ""; if (void 0 !== d) for (var c = d.length, h = 0; h < c; h++) { var f = d[h]; p += y(f, a, t, "diagramBg", r) } return "<div class='block diagram-content' style='" + il(s, a, void 0, void 0, r) + nl(s, void 0, void 0) + "'>" + p + "</div>" }(a, t, e); break; case "http://schemas.openxmlformats.org/presentationml/2006/ole": var o = ul(a, ["a:graphic", "a:graphicData", "mc:AlternateContent", "mc:Fallback", "p:oleObj"]); void 0 === o && (o = ul(a, ["a:graphic", "a:graphicData", "p:oleObj"])), void 0 !== o && (s = m(o, t, r)) }return s }(t, e, s, o); break; case "p:grpSp": L = m(t, e, s); break; case "mc:AlternateContent": L = m(ul(t, ["mc:Fallback"]), e, s) }return L } function m(a, t, r) { var e, s, o, i, n, l, d, p, c, h = ul(a, ["p:grpSpPr", "a:xfrm"]); void 0 !== h && (e = parseInt(h["a:off"].attrs.x) * tl, s = parseInt(h["a:off"].attrs.y) * tl, l = parseInt(h["a:chOff"].attrs.x) * tl, n = parseInt(h["a:chOff"].attrs.y) * tl, o = parseInt(h["a:ext"].attrs.cx) * tl, f = parseInt(h["a:ext"].attrs.cy) * tl, d = parseInt(h["a:chExt"].attrs.cx) * tl, p = parseInt(h["a:chExt"].attrs.cy) * tl, h = parseInt(h.attrs.rot), i = "", n = s - n, l = e - l, d = o - d, p = f - p, c = "group", isNaN(h) || (i += "transform: rotate(" + (h = vl(h)) + "deg) ; transform-origin: center;", 0 != h && (n = s, l = e, d = o, p = f, c = "group-rotate"))); var f = ""; void 0 !== i && "" != i && (f += i), void 0 !== n && (f += "top: " + n + "px;"), void 0 !== l && (f += "left: " + l + "px;"), void 0 !== d && (f += "width:" + d + "px;"), void 0 !== p && (f += "height: " + p + "px;"); var u, v = "<div class='block group' style='z-index: " + a.attrs.order + ";" + f + " border:1px solid red;'>"; for (u in a) if (a[u].constructor === Array) for (var L = 0; L < a[u].length; L++)v += z(u, a[u][L], a, t, r, c); else v += z(u, a[u], a, t, r, c); return v += "</div>" } function y(a, t, r, e, s) { var o, i = ul(a, ["p:nvSpPr", "p:cNvPr", "attrs", "id"]), n = ul(a, ["p:nvSpPr", "p:cNvPr", "attrs", "name"]), l = void 0 === ul(a, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "idx"]) ? void 0 : ul(a, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "idx"]), d = void 0 === ul(a, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"]) ? void 0 : ul(a, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"]), p = ul(a, ["attrs", "order"]); "slideLayoutBg" != e && "slideMasterBg" != e || (o = "1" == ul(a, ["p:nvSpPr", "p:nvPr", "attrs", "userDrawn"])); var c = void 0, h = void 0; return void 0 !== l ? (c = r.slideLayoutTables.idxTable[l], h = void 0 !== d ? r.slideMasterTables.typeTable[d] : r.slideMasterTables.idxTable[l]) : void 0 !== d && (c = r.slideLayoutTables.typeTable[d], h = r.slideMasterTables.typeTable[d]), void 0 === d && (txBoxVal = ul(a, ["p:nvSpPr", "p:cNvSpPr", "attrs", "txBox"]), "1" == txBoxVal && (d = "textBox")), void 0 === d && void 0 === (d = ul(c, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"])) && (d = "diagramBg" == e ? "diagram" : "obj"), k(a, t, c, h, i, n, l, d, p, r, o, s, e) } function k(a, t, r, e, s, o, i, n, l, d, p, c, h) { var f = ["p:spPr", "a:xfrm"], u = ul(a, f), v = ul(r, f), L = ul(e, f), g = "", b = ul(a, ["attrs", "order"]), m = ul(a, ["p:spPr", "a:prstGeom", "attrs", "prst"]), y = ul(a, ["p:spPr", "a:custGeom"]), k = !1, M = !1, f = ""; "1" === ul(u, ["attrs", "flipV"]) && (k = !0), "1" === ul(u, ["attrs", "flipH"]) && (M = !0), M && !k ? f = " scale(-1,1)" : !M && k ? f = " scale(1,-1)" : M && k && (f = " scale(-1,-1)"); var x, P, w, I, _, C, S, T, A, D, G, j, M = vl(ul(u, ["attrs", "rot"])), k = ul(a, ["p:txXfrm"]); if (void 0 !== k ? void 0 !== (k = ul(k, ["attrs", "rot"])) && (x = vl(k) + 90) : x = M, void 0 === m && void 0 === y || (Fn = ul(u, ["a:off", "attrs"]), parseInt(Fn.x), parseInt(Fn.y), Nn = ul(u, ["a:ext", "attrs"]), P = parseInt(Nn.cx) * tl, w = parseInt(Nn.cy) * tl, g += "<svg class='drawing " + (Fn = "_svg_css_" + (Object.keys(rl).length + 1) + "_" + Math.floor(1001 * Math.random())) + " " + (Nn = Fn + "_effects") + " ' _id='" + s + "' _idx='" + i + "' _type='" + n + "' _name='" + o + "'' style='" + il(u, t, void 0, void 0, c) + nl(u, void 0, void 0) + " z-index: " + l + ";transform: rotate(" + (void 0 !== M ? M : 0) + "deg)" + f + ";'>", g += "<defs>", I = cl(a, t, !0, d, h), C = _ = !1, "GROUP_FILL" == (M = hl(ul(a, ["p:spPr"]))) && (M = hl(ul(t, ["p:grpSpPr"]))), "GRADIENT_FILL" == M ? (_ = !0, j = I.color, g += function (a, t, r, e, s) { var o = function (a) { var t = ["0%", "100%"]; { if (0 == a) return t; for (var r = a; r--;) { var e = 100 - 100 / (a + 1) * (r + 1) + "%"; t.splice(-1, 0, e) } } return t }(e - 2), i = "", n = function (a, t, r) { var e = parseFloat(r), s = parseFloat(t), o = parseFloat(a), i = 2, n = 2, l = e / 2, d = s / 2, p = 2, c = 2, h = 2, r = 2, t = (o % 360 + 360) % 360, a = (360 - t) * Math.PI / 180, o = Math.tan(a), a = d - o * l; 0 == t ? (p = e, h = 0, r = c = d) : t < 90 ? (n = e, i = 0) : 90 == t ? (c = 0, h = p = l, r = s) : t < 180 ? i = n = 0 : 180 == t ? (p = 0, h = e, r = c = d) : t < 270 ? (n = 0, i = s) : 270 == t ? (c = s, h = p = l, r = 0) : (n = e, i = s); n = i + n / o, p = 2 == p ? o * (n - a) / (Math.pow(o, 2) + 1) : p, c = 2 == c ? o * p + a : c, h = 2 == h ? e - p : h, r = 2 == r ? s - c : r, h = Math.round(h / e * 100 * 100) / 100, r = Math.round(r / s * 100 * 100) / 100, e = Math.round(p / e * 100 * 100) / 100, s = Math.round(c / s * 100 * 100) / 100; return [h, r, e, s] }(r, t, a), r = n[0], t = n[1], a = n[2], n = n[3], l = o.length, d = l < 20 ? 100 : 1e3; i += '<linearGradient id="linGrd_' + s + '"' + (' gradientUnits="userSpaceOnUse" x1="' + r + '%" y1="' + t + '%" x2="' + a + '%" y2="' + n + '%"') + ">\n"; for (var p = 0; p < l; p++) { var c = tinycolor("#" + e[p]), h = c.getAlpha(); i += '<stop offset="' + Math.round(parseFloat(o[p]) / 100 * d) / d + '" style="stop-color:' + c.toHexString() + "; stop-opacity:" + h + ';"', i += "/>\n" } return i += "</linearGradient>\n" }(P, w, I.rot + 90, j, b)) : "PIC_FILL" == M ? (C = !0, g += function (a, t, r, e) { var s = function (a) { var t = new Image; t.onload = function () { t.width, t.height }, t.src = a; do { if (void 0 !== t.width) return [t.width, t.height] } while (void 0 === t.width) }(t), o = s[0], i = s[1], s = ul(a["p:spPr"]["a:blipFill"], ["a:tile", "attrs"]); { var n, l; void 0 !== s && void 0 !== s.sx && (n = parseInt(s.sx) / 1e5 * o, l = parseInt(s.sy) / 1e5 * i) } s = a["p:spPr"]["a:blipFill"]["a:blip"], i = ul(s, ["a:alphaModFix", "attrs"]), a = ""; void 0 !== i && void 0 !== i.amt && "" != i.amt && (a = "opacity='" + parseInt(i.amt) / 1e5 + "'"); i = void 0 !== n && 0 != n ? '<pattern id="imgPtrn_' + r + '" x="0" y="0"  width="' + n + '" height="' + l + '" patternUnits="userSpaceOnUse">' : '<pattern id="imgPtrn_' + r + '"  patternContentUnits="objectBoundingBox"  width="1" height="1">'; var d = ul(s, ["a:duotone"]), r = "", s = ""; { var p; void 0 !== d && (p = [], Object.keys(d).forEach(function (a) { var t; "attrs" != a && ((t = {})[a] = d[a], t = fl(t, void 0, void 0, e), t = tinycolor("#" + t), p.push(t.toRgb())) }), 2 == p.length && (r = '<filter id="svg_image_duotone"> <feColorMatrix type="matrix" values=".33 .33 .33 0 0.33 .33 .33 0 0.33 .33 .33 0 00 0 0 1 0"></feColorMatrix><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="table" tableValues="' + p[0].r / 255 + " " + p[1].r / 255 + '"></feFuncR><feFuncG type="table" tableValues="' + p[0].g / 255 + " " + p[1].g / 255 + '"></feFuncG><feFuncB type="table" tableValues="' + p[0].b / 255 + " " + p[1].b / 255 + '"></feFuncB></feComponentTransfer> </filter>'), s = 'filter="url(#svg_image_duotone)"', i += r) } t = Ll(t), i += void 0 !== n && 0 != n ? '<image  xlink:href="' + t + '" x="0" y="0" width="' + n + '" height="' + l + '" ' + a + " " + s + "></image>" : '<image  xlink:href="' + t + '" preserveAspectRatio="none" width="1" height="1" ' + a + " " + s + "></image>"; return i += "</pattern>" }(a, I, b, d)) : "PATTERN_FILL" == M ? ((G = I) in rl && (G += "do-nothing: " + Fn + ";"), rl[G] = { name: Fn, text: G }, I = "none") : "SOLID_FILL" == M || "PATTERN_FILL" == M || "arc" != m && "bracketPair" != m && "bracePair" != m && "leftBracket" != m && "leftBrace" != m && "rightBrace" != m && "rightBracket" != m || (I = "none"), S = pl(a, 0, !0, "shape", d), T = ul(a, ["p:spPr", "a:ln", "a:headEnd", "attrs"]), A = ul(a, ["p:spPr", "a:ln", "a:tailEnd", "attrs"]), D = "", void 0 !== (f = ul(a, ["p:spPr", "a:effectLst", "a:outerShdw"])) && (Vn = fl(f, void 0, void 0, d), G = (j = f.attrs).dir ? parseInt(j.dir) / 6e4 : 0, M = parseInt(j.dist) * tl, f = j.blurRad ? parseInt(j.blurRad) * tl : "", j = M * Math.sin(G * Math.PI / 180), (Vn = "filter:drop-shadow(" + M * Math.cos(G * Math.PI / 180) + "px " + j + "px " + f + "px #" + Vn + ");") in rl && (Vn += "do-nothing: " + Fn + ";"), rl[Vn] = { name: Nn, text: Vn }), (void 0 === T || "triangle" !== T.type && "arrow" !== T.type) && (void 0 === A || "triangle" !== A.type && "arrow" !== A.type) || (g += "<marker id='markerTriangle_" + b + "' viewBox='0 0 10 10' refX='1' refY='5' markerWidth='5' markerHeight='5' stroke='" + S.color + "' fill='" + S.color + "' orient='auto-start-reverse' markerUnits='strokeWidth'><path d='M 0 0 L 10 5 L 0 10 z' /></marker>"), g += "</defs>"), void 0 !== m && void 0 === y) { switch (m) { case "rect": case "flowChartProcess": case "flowChartPredefinedProcess": case "flowChartInternalStorage": case "actionButtonBlank": g += "<rect x='0' y='0' width='" + P + "' height='" + w + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' " + D + "  />", "flowChartPredefinedProcess" == m ? g += "<rect x='" + P * (1 / 8) + "' y='0' width='" + .75 * P + "' height='" + w + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />" : "flowChartInternalStorage" == m && (g += " <polyline points='" + P * (1 / 8) + " 0," + P * (1 / 8) + " " + w + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />", g += " <polyline points='0 " + w * (1 / 8) + "," + P + " " + w * (1 / 8) + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"); break; case "flowChartCollate": g += "<path d='" + (ga = "M 0,0 L" + P + ",0 L0," + w + " L" + P + "," + w + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartDocument": g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + (Rs = 17322 * w / 21600) + " C" + (Ds = 10800 * P / 21600) + "," + Rs + " " + Ds + "," + (Bs = 23922 * w / 21600) + " 0," + (zs = 20172 * w / 21600) + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartMultidocument": g += "<path d='" + (ga = "M0," + (zs = 3675 * w / 21600) + " L" + (xs = 18595 * P / 21600) + "," + zs + " L" + xs + "," + (Rs = 18022 * w / 21600) + " C" + (js = 9298 * P / 21600) + "," + Rs + " " + js + "," + (Bs = 23542 * w / 21600) + " 0," + (er = 20782 * w / 21600) + " zM" + (Ds = 1532 * P / 21600) + "," + zs + " L" + Ds + "," + (pr = 1815 * w / 21600) + " L" + (Gs = 2e4 * P / 21600) + "," + pr + " L" + Gs + "," + (Is = 16252 * w / 21600) + " C" + (dr = 19298 * P / 21600) + "," + Is + " " + xs + "," + (ts = 16352 * w / 21600) + " " + xs + "," + ts + "M" + (Ls = 2972 * P / 21600) + "," + pr + " L" + Ls + ",0 L" + P + ",0 L" + P + "," + (as = 14392 * w / 21600) + " C" + (vs = 20800 * P / 21600) + "," + as + " " + Gs + "," + (sr = 14467 * w / 21600) + " " + Gs + "," + sr) + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonBackPrevious": var R = w / 2; g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " zM" + (Sr = (z = P / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + "," + R + " L" + (Yr = z + As) + "," + (_r = R - As) + " L" + Yr + "," + (Cr = R + As) + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonBeginning": var R = w / 2; g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " zM" + (Gr = (Sr = (z = P / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + (Dr = (Tr = 3 * Hr / 4) / 4)) + "," + R + " L" + (Yr = z + As) + "," + (_r = R - As) + " L" + Yr + "," + (Cr = R + As) + " zM" + (Or = Sr + (Ar = Tr / 8)) + "," + _r + " L" + Sr + "," + _r + " L" + Sr + "," + Cr + " L" + Or + "," + Cr + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonDocument": var z = P / 2, R = w / 2; As = 3 * (Hr = Math.min(P, w)) / 8, g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " zM" + (Sr = z - (Ns = 9 * Hr / 32)) + "," + (_r = R - As) + " L" + (Ar = (Yr = z + Ns) - (Tr = 3 * Hr / 16)) + "," + _r + " L" + Yr + "," + (Dr = _r + Tr) + " L" + Yr + "," + (Cr = R + As) + " L" + Sr + "," + Cr + " zM" + Ar + "," + _r + " L" + Ar + "," + Dr + " L" + Yr + "," + Dr + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonEnd": R = w / 2; g += "<path d='" + (ga = "M0," + w + " L" + P + "," + w + " L" + P + ",0 L0,0 z M" + (Gr = (Sr = (z = P / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + (Dr = 7 * (Tr = 3 * Hr / 4) / 8)) + "," + (_r = R - As) + " L" + (Yr = z + As) + "," + _r + " L" + Yr + "," + (Cr = R + As) + " L" + Gr + "," + Cr + " z M" + (Or = Sr + (Ar = 3 * Tr / 4)) + "," + R + " L" + Sr + "," + _r + " L" + Sr + "," + Cr + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonForwardNext": R = w / 2; g += "<path d='" + (ga = "M0," + w + " L" + P + "," + w + " L" + P + ",0 L0,0 z M" + (Yr = (z = P / 2) + (As = 3 * (Hr = Math.min(P, w)) / 8)) + "," + R + " L" + (Sr = z - As) + "," + (_r = R - As) + " L" + Sr + "," + (Cr = R + As) + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonHelp": var z = P / 2; aa = (_r = (R = w / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + (Er = 17 * (Tr = 3 * Hr / 4) / 28), ta = _r + 21 * Tr / 28, Z = Tr / 14; var B = (V = (Sr = z - As) + (Rr = 3 * Tr / 7)) + (Ar = Tr / 7), F = (E = _r + (Vr = 11 * Tr / 14)) + ($ = 3 * Tr / 28), N = ((Q = Sr + (zr = 4 * Tr / 7)) + V + (Or = 2 * Tr / 7)) / 2; g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " zM" + (q = Sr + (Dr = 3 * Tr / 14)) + "," + (la = _r + Or) + sl(qr = q + Or, la, Or, Or, 180, 360, !1).replace("M", "L") + sl(N, la, Ar, Dr, 0, 90, !1).replace("M", "L") + sl(N, aa, Z, $, 270, 180, !1).replace("M", "L") + " L" + Q + "," + ta + " L" + V + "," + ta + " L" + V + "," + aa + sl(B, aa, Ar, Dr, 180, 270, !1).replace("M", "L") + sl(Q, la, Z, $, 90, 0, !1).replace("M", "L") + sl(qr, la, Ar, Ar, 0, -180, !1).replace("M", "L") + " zM" + z + "," + E + sl(z, F, $, $, 270, 630, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonHome": g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (z = P / 2) + "," + (_r = (R = w / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + " L" + (Sr = z - As) + "," + R + " L" + (K = Sr + (Dr = (Tr = 3 * Hr / 4) / 8)) + "," + R + " L" + K + "," + (Cr = R + As) + " L" + (q = Sr + 7 * Tr / 8) + "," + Cr + " L" + q + "," + R + " L" + (Yr = z + As) + "," + R + " L" + (H = Sr + (Br = 13 * Tr / 16)) + "," + (Nr = _r + (Gr = 5 * Tr / 16)) + " L" + H + "," + (Vr = _r + (Ar = Tr / 16)) + " L" + (E = Sr + (zr = 11 * Tr / 16)) + "," + Vr + " L" + E + "," + (Fr = _r + (Or = 3 * Tr / 16)) + " z M" + (aa = Sr + (jr = 7 * Tr / 16)) + "," + (la = _r + (Er = 3 * Tr / 4)) + " L" + (ta = Sr + (Rr = 9 * Tr / 16)) + "," + la + " L" + ta + "," + Cr + " L" + aa + "," + Cr + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonInformation": z = P / 2; K = (_r = (R = w / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + (Gr = 5 * (Tr = 3 * Hr / 4) / 16), aa = _r + (jr = 3 * Tr / 8), ta = _r + 13 * Tr / 16, E = _r + (Vr = 7 * Tr / 8), H = (Sr = z - As) + Gr, W = Sr + (Rr = 13 * Tr / 32), U = Sr + (zr = 19 * Tr / 32), Q = Sr + (Br = 11 * Tr / 16); var O = (Fr = _r + (Ar = Tr / 32)) + (X = 3 * Tr / 32); g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " zM" + z + "," + _r + sl(z, Ur = _r + As, As, As, 270, 630, !1).replace("M", "L") + " zM" + z + "," + Fr + sl(z, O, X, X, 270, 630, !1).replace("M", "L") + "M" + H + "," + K + " L" + U + "," + K + " L" + U + "," + ta + " L" + Q + "," + ta + " L" + Q + "," + E + " L" + H + "," + E + " L" + H + "," + ta + " L" + W + "," + ta + " L" + W + "," + aa + " L" + H + "," + aa + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonMovie": var E, H, q, U, V, Q, X, Y, W, Z, $, J, K, aa, ta, z = P / 2; Cr = (R = w / 2) + (As = 3 * (Hr = Math.min(P, w)) / 8), _r = R - As, E = (Sr = z - As) + (Ar = 1455 * (Tr = 3 * Hr / 4) / 21600), g += "<path d='" + (ga = "M0," + w + " L" + P + "," + w + " L" + P + ",0 L0,0 zM" + Sr + "," + (Y = _r + (Br = 5280 * Tr / 21600)) + " L" + Sr + "," + (J = _r + (la = 9555 * Tr / 21600)) + " L" + E + "," + J + " L" + (H = Sr + (Dr = 1905 * Tr / 21600)) + "," + (J = _r + (Nr = 9067 * Tr / 21600)) + " L" + (q = Sr + (Or = 2325 * Tr / 21600)) + "," + J + " L" + q + "," + (ta = _r + (ta = 15592 * Tr / 21600)) + " L" + (U = Sr + (jr = 17010 * Tr / 21600)) + "," + ta + " L" + U + "," + (K = _r + (K = 13342 * Tr / 21600)) + " L" + (V = Sr + (Rr = 19335 * Tr / 21600)) + "," + K + " L" + (X = Sr + (Er = 20595 * Tr / 21600)) + "," + (aa = _r + (aa = 14580 * Tr / 21600)) + " L" + (Yr = z + As) + "," + aa + " L" + Yr + "," + (Z = _r + (Vr = 6630 * Tr / 21600)) + " L" + X + "," + Z + " L" + (Q = Sr + (zr = 19725 * Tr / 21600)) + "," + ($ = _r + (Fr = 7492 * Tr / 21600)) + " L" + U + "," + $ + " L" + U + "," + Z + " L" + (W = Sr + (Gr = 16155 * Tr / 21600)) + "," + (W = _r + 5730 * Tr / 21600) + " L" + H + "," + W + " L" + E + "," + Y + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonReturn": R = w / 2; Br = (Sr = (z = P / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + (Ar = 7 * (Tr = 3 * Hr / 4) / 8), Nr = Sr + (jr = Tr / 4); var O = (Rr = (_r = R - As) + (Dr = 3 * Tr / 4)) - (la = Tr / 8), ra = Fr = Sr + (Gr = 3 * Tr / 8), ea = (Cr = R + As) - Gr; g += "<path d='" + (ga = "M0," + w + " L" + P + "," + w + " L" + P + ",0 L0,0 z M" + (Yr = z + As) + "," + (Er = _r + jr) + " L" + (Sr + Dr) + "," + _r + " L" + z + "," + Er + " L" + (Vr = Sr + (Or = 5 * Tr / 8)) + "," + Er + " L" + Vr + "," + (zr = _r + Or) + sl(qr = Vr - la, zr, la, la, 0, 90, !1).replace("M", "L") + " L" + Fr + "," + Rr + sl(Fr, O, la, la, 90, 180, !1).replace("M", "L") + " L" + Nr + "," + Er + " L" + Sr + "," + Er + " L" + Sr + "," + zr + sl(ra, zr, Gr, Gr, 180, 90, !1).replace("M", "L") + " L" + z + "," + Cr + sl(z, ea, Gr, Gr, 90, 0, !1).replace("M", "L") + " L" + Br + "," + Er + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "actionButtonSound": R = w / 2; g += "<path d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Sr = (z = P / 2) - (As = 3 * (Hr = Math.min(P, w)) / 8)) + "," + (Er = (_r = R - As) + (Dr = 5 * (Tr = 3 * Hr / 4) / 16)) + " L" + (Vr = Sr + Dr) + "," + Er + " L" + (Fr = Sr + (Or = 5 * Tr / 8)) + "," + _r + " L" + Fr + "," + (Cr = R + As) + " L" + Vr + "," + (Br = _r + (Gr = 11 * Tr / 16)) + " L" + Sr + "," + Br + " z M" + (Nr = Sr + (jr = 3 * Tr / 4)) + "," + Er + " L" + (Yr = z + As) + "," + (zr = _r + (Ar = Tr / 8)) + " M" + Nr + "," + R + " L" + Yr + "," + R + " M" + Nr + "," + Br + " L" + Yr + "," + (_r + (Rr = 7 * Tr / 8))) + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "irregularSeal1": case "irregularSeal2": "irregularSeal1" == m ? ga = "M" + 10800 * P / 21600 + "," + 5800 * w / 21600 + " L" + 14522 * P / 21600 + ",0 L" + 14155 * P / 21600 + "," + 5325 * w / 21600 + " L" + 18380 * P / 21600 + "," + 4457 * w / 21600 + " L" + 16702 * P / 21600 + "," + 7315 * w / 21600 + " L" + 21097 * P / 21600 + "," + 8137 * w / 21600 + " L" + 17607 * P / 21600 + "," + 10475 * w / 21600 + " L" + P + "," + 13290 * w / 21600 + " L" + 16837 * P / 21600 + "," + 12942 * w / 21600 + " L" + 18145 * P / 21600 + "," + 18095 * w / 21600 + " L" + 14020 * P / 21600 + "," + 14457 * w / 21600 + " L" + 13247 * P / 21600 + "," + 19737 * w / 21600 + " L" + 10532 * P / 21600 + "," + 14935 * w / 21600 + " L" + 8485 * P / 21600 + "," + w + " L" + 7715 * P / 21600 + "," + 15627 * w / 21600 + " L" + 4762 * P / 21600 + "," + 17617 * w / 21600 + " L" + 5667 * P / 21600 + "," + 13937 * w / 21600 + " L" + 135 * P / 21600 + "," + 14587 * w / 21600 + " L" + 3722 * P / 21600 + "," + 11775 * w / 21600 + " L0," + 8615 * w / 21600 + " L" + 4627 * P / 21600 + "," + 7617 * w / 21600 + " L" + 370 * P / 21600 + "," + 2295 * w / 21600 + " L" + 7312 * P / 21600 + "," + 6320 * w / 21600 + " L" + 8352 * P / 21600 + "," + 2295 * w / 21600 + " z" : "irregularSeal2" == m && (ga = "M" + 11462 * P / 21600 + "," + 4342 * w / 21600 + " L" + 14790 * P / 21600 + ",0 L" + 14525 * P / 21600 + "," + 5777 * w / 21600 + " L" + 18007 * P / 21600 + "," + 3172 * w / 21600 + " L" + 16380 * P / 21600 + "," + 6532 * w / 21600 + " L" + P + "," + 6645 * w / 21600 + " L" + 16985 * P / 21600 + "," + 9402 * w / 21600 + " L" + 18270 * P / 21600 + "," + 11290 * w / 21600 + " L" + 16380 * P / 21600 + "," + 12310 * w / 21600 + " L" + 18877 * P / 21600 + "," + 15632 * w / 21600 + " L" + 14640 * P / 21600 + "," + 14350 * w / 21600 + " L" + 14942 * P / 21600 + "," + 17370 * w / 21600 + " L" + 12180 * P / 21600 + "," + 15935 * w / 21600 + " L" + 11612 * P / 21600 + "," + 18842 * w / 21600 + " L" + 9872 * P / 21600 + "," + 17370 * w / 21600 + " L" + 8700 * P / 21600 + "," + 19712 * w / 21600 + " L" + 7527 * P / 21600 + "," + 18125 * w / 21600 + " L" + 4917 * P / 21600 + "," + w + " L" + 4805 * P / 21600 + "," + 18240 * w / 21600 + " L" + 1285 * P / 21600 + "," + 17825 * w / 21600 + " L" + 3330 * P / 21600 + "," + 15370 * w / 21600 + " L0," + 12877 * w / 21600 + " L" + 3935 * P / 21600 + "," + 11592 * w / 21600 + " L" + 1172 * P / 21600 + "," + 8270 * w / 21600 + " L" + 5372 * P / 21600 + "," + 7817 * w / 21600 + " L" + 4502 * P / 21600 + "," + 3625 * w / 21600 + " L" + 8550 * P / 21600 + "," + 6382 * w / 21600 + " L" + 9722 * P / 21600 + "," + 1887 * w / 21600 + " z"), g += "<path d='" + ga + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartTerminator": var sa = 90; g += "<path d='" + (ga = "M" + (Ds = 3475 * P / 21600) + ",0 L" + (Gs = 18125 * P / 21600) + ",0" + sl(Gs, w / 2, Ds, Rs = 10800 * w / 21600, na = 270, na + (ia = 180), !1).replace("M", "L") + " L" + Ds + "," + w + sl(Ds, w / 2, Ds, Rs, sa, sa + ia, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartPunchedTape": zs = 18 * w / 20, g += "<path d='" + (ga = "M0," + (Rs = 2 * w / 20) + sl(Ds = 5 * P / 20, Rs, Ds, Rs, ia = 180, 0, !1).replace("M", "L") + sl(.75 * P, Rs, Ds, Rs, ia, 360, !1).replace("M", "L") + " L" + P + "," + zs + sl(.75 * P, zs, Ds, Rs, 0, -ia, !1).replace("M", "L") + sl(Ds, zs, Ds, Rs, 0, ia, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartOnlineStorage": sa = 90; g += "<path d='" + (ga = "M" + (Ds = +P / 6) + ",0 L" + P + ",0" + sl(P, w / 2, Ds, Rs = 3 * w / 6, na = 270, 90, !1).replace("M", "L") + " L" + Ds + "," + w + sl(Ds, w / 2, Ds, Rs, sa, 270, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartDisplay": g += "<path d='" + (ga = "M0," + (Rs = 3 * w / 6) + " L" + (Ds = +P / 6) + ",0 L" + (Gs = 5 * P / 6) + ",0" + sl(P, w / 2, Ds, Rs, na = 270, na + (ia = 180), !1).replace("M", "L") + " L" + Ds + "," + w + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartDelay": sa = 90; g += "<path d='" + (ga = "M0,0 L" + (oa = P / 2) + ",0" + sl(oa, Ca = w / 2, oa, Ca, na = 270, na + (ia = 180), !1).replace("M", "L") + " L0," + w + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "flowChartMagneticTape": var oa = P / 2, ia = 180, na = 270, sa = 90; bt = (Ca = w / 2) + (os = Ca * Math.sin(Math.PI / 4)); var la = 180 * Math.atan(w / P) / Math.PI; g += "<path d='" + (ga = "M" + oa + "," + w + sl(oa, Ca, oa, Ca, sa, ia, !1).replace("M", "L") + sl(oa, Ca, oa, Ca, ia, na, !1).replace("M", "L") + sl(oa, Ca, oa, Ca, na, 360, !1).replace("M", "L") + sl(oa, Ca, oa, Ca, 0, la, !1).replace("M", "L") + " L" + P + "," + bt + " L" + P + "," + w + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "ellipse": case "flowChartConnector": case "flowChartSummingJunction": case "flowChartOr": g += "<ellipse cx='" + P / 2 + "' cy='" + w / 2 + "' rx='" + P / 2 + "' ry='" + w / 2 + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />", "flowChartOr" == m ? (g += " <polyline points='" + P / 2 + " 0," + P / 2 + " " + w + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />", g += " <polyline points='0 " + w / 2 + "," + P + " " + w / 2 + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />") : "flowChartSummingJunction" == m && (oa = z = P / 2, Ca = R = w / 2, ca = Math.PI / 4, g += " <polyline points='" + (vt = z - (es = oa * Math.cos(ca))) + " " + (Lt = R - (os = Ca * Math.sin(ca))) + "," + (gt = z + es) + " " + (bt = R + os) + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />", g += " <polyline points='" + gt + " " + Lt + "," + vt + " " + bt + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"); break; case "roundRect": case "round1Rect": case "round2DiagRect": case "round2SameRect": case "snip1Rect": case "snip2DiagRect": case "snip2SameRect": case "flowChartAlternateProcess": case "flowChartPunchedCard": var da, pa, ca, ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]); if (void 0 !== ha && ha.constructor === Array) for (var fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = parseInt(Zs.substr(4)) / 5e4) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 5e4); else void 0 !== ha && ha.constructor !== Array && (ca = ul(ha, ["attrs", "fmla"]), va = parseInt(ca.substr(4)) / 5e4, La = 0); var ua = ""; switch (m) { case "roundRect": case "flowChartAlternateProcess": da = "round", pa = "cornrAll", void 0 === va && (va = .33334), La = 0; break; case "round1Rect": da = "round", pa = "cornr1", void 0 === va && (va = .33334), La = 0; break; case "round2DiagRect": da = "round", pa = "diag", void 0 === va && (va = .33334), void 0 === La && (La = 0); break; case "round2SameRect": da = "round", pa = "cornr2", void 0 === va && (va = .33334), void 0 === La && (La = 0); break; case "snip1Rect": case "flowChartPunchedCard": da = "snip", pa = "cornr1", void 0 === va && (va = .33334), La = 0, "flowChartPunchedCard" == m && (ua = "transform='translate(" + P + ",0) scale(-1,1)'"); break; case "snip2DiagRect": da = "snip", pa = "diag", void 0 === va && (va = 0), void 0 === La && (La = .33334); break; case "snip2SameRect": da = "snip", pa = "cornr2", void 0 === va && (va = .33334), void 0 === La && (La = 0) }g += "<path " + ua + "  d='" + (zn = function (a, t, r, e, s, o) { var i, n, l, d, p; "cornr1" == o ? (l = n = i = 0, d = r) : "cornr2" == o ? (l = n = e, d = i = r) : "cornrAll" == o ? d = l = n = i = r : "diag" == o && (l = i = r, d = n = e); "round" == s ? p = "M0," + (t / 2 + t / 2 * (1 - n)) + " Q0," + t + " " + n * (a / 2) + "," + t + " L" + (a / 2 + a / 2 * (1 - l)) + "," + t + " Q" + a + "," + t + " " + a + "," + (t / 2 + t / 2 * (1 - l)) + "L" + a + "," + t / 2 * d + " Q" + a + ",0 " + (a / 2 + a / 2 * (1 - d)) + ",0 L" + a / 2 * i + ",0 Q0,0 0," + t / 2 * i + " z" : "snip" == s && (p = "M0," + i * (t / 2) + " L0," + (t / 2 + t / 2 * (1 - n)) + "L" + n * (a / 2) + "," + t + " L" + (a / 2 + a / 2 * (1 - l)) + "," + t + "L" + a + "," + (t / 2 + t / 2 * (1 - l)) + " L" + a + "," + d * (t / 2) + "L" + (a / 2 + a / 2 * (1 - d)) + ",0 L" + a / 2 * i + ",0 z"); return p }(P, w, va, La, da, pa)) + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "snipRoundRect": var va = .33334, La = .33334; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = parseInt(Zs.substr(4)) / 5e4) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 5e4); g += "<path   d='" + (zn = "M0," + w + " L" + P + "," + w + " L" + P + "," + w / 2 * La + " L" + (P / 2 + P / 2 * (1 - La)) + ",0 L" + P / 2 * va + ",0 Q0,0 0," + w / 2 * va + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "bentConnector2": var ga = ""; g += "<path d='" + (ga = "M " + P + " 0 L " + P + " " + w + " L 0 " + w) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' fill='none' ", void 0 === T || "triangle" !== T.type && "arrow" !== T.type || (g += "marker-start='url(#markerTriangle_" + b + ")' "), void 0 === A || "triangle" !== A.type && "arrow" !== A.type || (g += "marker-end='url(#markerTriangle_" + b + ")' "), g += "/>"; break; case "rtTriangle": g += " <polygon points='0 0,0 " + w + "," + P + " " + w + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "triangle": case "flowChartExtract": case "flowChartMerge": var ba = .5; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (ba = parseInt(Ma.substr(4)) * tl), g += " <polygon " + (ua = "flowChartMerge" == m ? "transform='rotate(180 " + P / 2 + "," + w / 2 + ")'" : "") + " points='" + P * ba + " 0,0 " + w + "," + P + " " + w + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "diamond": case "flowChartDecision": case "flowChartSort": g += " <polygon points='" + P / 2 + " 0,0 " + w / 2 + "," + P / 2 + " " + w + "," + P + " " + w / 2 + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />", "flowChartSort" == m && (g += " <polyline points='0 " + w / 2 + "," + P + " " + w / 2 + "' fill='none' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"); break; case "trapezoid": case "flowChartManualOperation": case "flowChartManualInput": var ma = .2, ya = .7407; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (ma = .5 * (parseInt(Ma.substr(4)) * tl) / ya); var ka = 0; "flowChartManualInput" == m && (ma = 0, ka = w / 5), g += " <polygon " + (ua = "flowChartManualOperation" == m ? "transform='rotate(180 " + P / 2 + "," + w / 2 + ")'" : "") + " points='" + P * ma + " " + ka + ",0 " + w + "," + P + " " + w + "," + (1 - ma) * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "parallelogram": case "flowChartInputOutput": ma = .25; ya = w < P ? P / w : w / P, void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (ma = parseInt(Ma.substr(4)) / 1e5 / ya), g += " <polygon points='" + ma * P + " 0,0 " + w + "," + (1 - ma) * P + " " + w + "," + P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "pentagon": g += " <polygon points='" + .5 * P + " 0,0 " + .375 * w + "," + .15 * P + " " + w + "," + .85 * P + " " + w + "," + P + " " + .375 * w + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "hexagon": case "flowChartPreparation": var Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 25e3 * tl, Pa = 115470 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, _a = 60 * Math.PI / 180; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); var R = w / 2, Ca = w / 2; Aa = wa * P / (Hr = Math.min(P, w)), Gs = P - (Ds = Hr * (Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa) / Ia), g += "<path   d='" + (ga = "M0," + R + " L" + Ds + "," + (Rs = R - (Ts = (Ga = Ca * Pa / Ia) * Math.sin(_a))) + " L" + Gs + "," + Rs + " L" + P + "," + R + " L" + Gs + "," + (zs = R + Ts) + " L" + Ds + "," + zs + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "heptagon": g += " <polygon points='" + .5 * P + " 0," + P / 8 + " " + w / 4 + ",0 " + 5 / 8 * w + "," + P / 4 + " " + w + "," + .75 * P + " " + w + "," + P + " " + 5 / 8 * w + "," + 7 / 8 * P + " " + w / 4 + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "octagon": var Sa = .25; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Sa = parseInt(Ma.substr(4)) / 1e5), g += " <polygon points='" + Sa * P + " 0,0 " + Sa * w + ",0 " + (wt = 1 - Sa) * w + "," + Sa * P + " " + w + "," + wt * P + " " + w + "," + P + " " + wt * w + "," + P + " " + Sa * w + "," + wt * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "decagon": g += " <polygon points='" + 3 / 8 * P + " 0," + P / 8 + " " + w / 8 + ",0 " + w / 2 + "," + P / 8 + " " + 7 / 8 * w + "," + 3 / 8 * P + " " + w + "," + 5 / 8 * P + " " + w + "," + 7 / 8 * P + " " + 7 / 8 * w + "," + P + " " + w / 2 + "," + 7 / 8 * P + " " + w / 8 + "," + 5 / 8 * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "dodecagon": g += " <polygon points='" + 3 / 8 * P + " 0," + P / 8 + " " + w / 8 + ",0 " + 3 / 8 * w + ",0 " + 5 / 8 * w + "," + P / 8 + " " + 7 / 8 * w + "," + 3 / 8 * P + " " + w + "," + 5 / 8 * P + " " + w + "," + 7 / 8 * P + " " + 7 / 8 * w + "," + P + " " + 5 / 8 * w + "," + P + " " + 3 / 8 * w + "," + 7 / 8 * P + " " + w / 8 + "," + 5 / 8 * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star4": z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, xa = 19098 * tl, wa = 5e4 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && "adj" == (o = Ma.attrs.name) && (xa = parseInt(Ma.attrs.fmla.substr(4)) * tl), At = Ca * (Vs = xa < 0 ? 0 : wa < xa ? wa : xa) / wa, g += "<path   d='" + (ga = "M0," + R + " L" + (Ko = z - (Wr = (Tt = oa * Vs / wa) * Math.cos(.7853981634))) + "," + (ai = R - (Zr = At * Math.sin(.7853981634))) + " L" + z + ",0 L" + (ti = z + Wr) + "," + ai + " L" + P + "," + R + " L" + ti + "," + (ri = R + Zr) + " L" + z + "," + w + " L" + Ko + "," + ri + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star5": var z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, xa = 19098 * tl, Ta = 105146 * tl, Pa = 110557 * tl, Aa = 5e4 * tl, wa = 1e5 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && Object.keys(Ma).forEach(function (a) { var t = Ma[a].attrs.name; "adj" == t ? xa = parseInt(Ma[a].attrs.fmla.substr(4)) * tl : "hf" == t ? Ta = parseInt(Ma[a].attrs.fmla.substr(4)) * tl : "vf" == t && (Pa = parseInt(Ma[a].attrs.fmla.substr(4)) * tl) }), Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, Ga = Ca * Pa / wa, ja = R * Pa / wa, Ds = z - (Ns = (Ra = oa * Ta / wa) * Math.cos(.31415926536)), Gs = z - (As = Ra * Math.cos(5.3407075111)), js = z + As, dr = z + Ns, Rs = ja - (Ts = Ga * Math.sin(.31415926536)), zs = ja - (Os = Ga * Math.sin(5.3407075111)), At = Ga * Vs / Aa, Oa = (Tt = Ra * Vs / Aa) * Math.cos(5.9690260418), g += "<path   d='" + (ga = "M" + Ds + "," + Rs + " L" + (ti = z - (Na = Tt * Math.cos(.94247779608))) + "," + (ai = ja - (ut = At * Math.sin(.94247779608))) + " L" + z + ",0 L" + (Ea = z + Na) + "," + ai + " L" + dr + "," + Rs + " L" + (Ha = z + Oa) + "," + (ri = ja - (ft = At * Math.sin(5.9690260418))) + " L" + js + "," + zs + " L" + z + "," + (tt = ja + At) + " L" + Gs + "," + zs + " L" + (Ko = z - Oa) + "," + ri + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star6": var z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, Da = w / 4, xa = 28868 * tl, Ta = 115470 * tl, Aa = 5e4 * tl, wa = 1e5 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && Object.keys(Ma).forEach(function (a) { var t = Ma[a].attrs.name; "adj" == t ? xa = parseInt(Ma[a].attrs.fmla.substr(4)) * tl : "hf" == t && (Ta = parseInt(Ma[a].attrs.fmla.substr(4)) * tl) }), Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, Gs = z + (Ns = (Ra = oa * Ta / wa) * Math.cos(.5235987756)), zs = R + Da, Ko = z - (Tt = Ra * Vs / Aa), Ea = z + (Na = Tt / 2), Ha = z + Tt, At = Ca * Vs / Aa, g += "<path   d='" + (ga = "M" + (Ds = z - Ns) + "," + Da + " L" + (ti = z - Na) + "," + (ai = R - (ut = At * Math.sin(1.0471975512))) + " L" + z + ",0 L" + Ea + "," + ai + " L" + Gs + "," + Da + " L" + Ha + "," + R + " L" + Gs + "," + zs + " L" + Ea + "," + (ri = R + ut) + " L" + z + "," + w + " L" + ti + "," + ri + " L" + Ds + "," + zs + " L" + Ko + "," + R + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star7": var Ga, ja, z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, xa = 34601 * tl, Ta = 102572 * tl, Pa = 105210 * tl, Aa = 5e4 * tl, wa = 1e5 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && Object.keys(Ma).forEach(function (a) { var t = Ma[a].attrs.name; "adj" == t ? xa = parseInt(Ma[a].attrs.fmla.substr(4)) * tl : "hf" == t ? Ta = parseInt(Ma[a].attrs.fmla.substr(4)) * tl : "vf" == t && (Pa = parseInt(Ma[a].attrs.fmla.substr(4)) * tl) }), ja = R * Pa / wa, At = (Ga = Ca * Pa / wa) * (Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa) / Aa, g += "<path   d='" + (ga = "M" + (Ds = z - (Ns = 97493 * (Ra = oa * Ta / wa) / 1e5)) + "," + (zs = ja + (Os = 22252 * Ga / 1e5)) + " L" + (Ko = z - (Oa = 97493 * (Tt = Ra * Vs / Aa) / 1e5)) + "," + (ri = ja - (ft = 22252 * At / 1e5)) + " L" + (Gs = z - (As = 78183 * Ra / 1e5)) + "," + (Rs = ja - (Ts = 62349 * Ga / 1e5)) + " L" + (Ea = z - (at = 43388 * Tt / 1e5)) + "," + (ai = ja - (ut = 90097 * At / 1e5)) + " L" + z + ",0 L" + (Ha = z + at) + "," + ai + " L" + (xs = z + As) + "," + Rs + " L" + (Ua = z + Oa) + "," + ri + " L" + (Ls = z + Ns) + "," + zs + " L" + (qa = z + (Na = 78183 * Tt / 1e5)) + "," + (tt = ja + (ht = 62349 * At / 1e5)) + " L" + (dr = z + ($e = 43388 * Ra / 1e5)) + "," + (Bs = ja + (Es = 90097 * Ga / 1e5)) + " L" + z + "," + (rt = ja + At) + " L" + (js = z - $e) + "," + Bs + " L" + (ti = z - Na) + "," + tt + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star8": z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, xa = 37500 * tl, Aa = 5e4 * tl, wa = 1e5 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && "adj" == (o = Ma.attrs.name) && (xa = parseInt(Ma.attrs.fmla.substr(4)) * tl), Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, Gs = z + (Ns = oa * Math.cos(.7853981634)), At = Ca * Vs / Aa, g += "<path   d='" + (ga = "M0," + R + " L" + (Ko = z - (Oa = 92388 * (Tt = oa * Vs / Aa) / 1e5)) + "," + (ri = R - (ft = 38268 * At / 1e5)) + " L" + (Ds = z - Ns) + "," + (Rs = R - (Ts = Ca * Math.sin(.7853981634))) + " L" + (ti = z - (Na = 38268 * Tt / 1e5)) + "," + (ai = R - (ut = 92388 * At / 1e5)) + " L" + z + ",0 L" + (Ea = z + Na) + "," + ai + " L" + Gs + "," + Rs + " L" + (Ha = z + Oa) + "," + ri + " L" + P + "," + R + " L" + Ha + "," + (tt = R + ft) + " L" + Gs + "," + (zs = R + Ts) + " L" + Ea + "," + (rt = R + ut) + " L" + z + "," + w + " L" + ti + "," + rt + " L" + Ds + "," + zs + " L" + Ko + "," + tt + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star10": var Ra, z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, xa = 42533 * tl, Ta = 105146 * tl, Aa = 5e4 * tl, wa = 1e5 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && Object.keys(Ma).forEach(function (a) { var t = Ma[a].attrs.name; "adj" == t ? xa = parseInt(Ma[a].attrs.fmla.substr(4)) * tl : "hf" == t && (Ta = parseInt(Ma[a].attrs.fmla.substr(4)) * tl) }), At = Ca * (Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa) / Aa, g += "<path   d='" + (ga = "M" + (Ds = z - (Ns = 95106 * (Ra = oa * Ta / wa) / 1e5)) + "," + (zs = R - (Os = 30902 * Ca / 1e5)) + " L" + (ti = z - (Oa = 80902 * (Tt = Ra * Vs / Aa) / 1e5)) + "," + (ri = R - (ft = 58779 * At / 1e5)) + " L" + (Gs = z - (As = 58779 * Ra / 1e5)) + "," + (Rs = R - (Ts = 80902 * Ca / 1e5)) + " L" + (Ea = z - (Na = 30902 * Tt / 1e5)) + "," + (ai = R - (ut = 95106 * At / 1e5)) + " L" + z + ",0 L" + (Ha = z + Na) + "," + ai + " L" + (js = z + As) + "," + Rs + " L" + (qa = z + Oa) + "," + ri + " L" + (dr = z + Ns) + "," + zs + " L" + (Ua = z + Tt) + "," + R + " L" + dr + "," + (Bs = R + Os) + " L" + qa + "," + (tt = R + ft) + " L" + js + "," + (pr = R + Ts) + " L" + Ha + "," + (rt = R + ut) + " L" + z + "," + w + " L" + Ea + "," + rt + " L" + Gs + "," + pr + " L" + ti + "," + tt + " L" + Ds + "," + Bs + " L" + (Ko = z - Tt) + "," + R + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star12": var z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, Da = w / 4, za = P / 4, xa = 37500 * tl, Aa = 5e4 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && "adj" == (o = Ma.attrs.name) && (xa = parseInt(Ma.attrs.fmla.substr(4)) * tl), Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, Ds = z - (Ns = oa * Math.cos(.5235987756)), js = 3 * P / 4, dr = z + Ns, Rs = R - (Ts = Ca * Math.sin(1.0471975512)), Bs = 3 * w / 4, pr = R + Ts, At = Ca * Vs / Aa, Oa = (Tt = oa * Vs / Aa) * Math.cos(.2617993878), Na = Tt * Math.cos(.7853981634), at = Tt * Math.cos(1.308996939), ut = At * Math.sin(1.308996939), ft = At * Math.sin(.7853981634), g += "<path   d='" + (ga = "M0," + R + " L" + (Ko = z - Oa) + "," + (tt = R - (ht = At * Math.sin(.2617993878))) + " L" + Ds + "," + Da + " L" + (ti = z - Na) + "," + (ri = R - ft) + " L" + za + "," + Rs + " L" + (Ea = z - at) + "," + (ai = R - ut) + " L" + z + ",0 L" + (Ha = z + at) + "," + ai + " L" + js + "," + Rs + " L" + (qa = z + Na) + "," + ri + " L" + dr + "," + Da + " L" + (Ua = z + Oa) + "," + tt + " L" + P + "," + R + " L" + Ua + "," + (rt = R + ht) + " L" + dr + "," + Bs + " L" + qa + "," + (et = R + ft) + " L" + js + "," + pr + " L" + Ha + "," + (st = R + ut) + " L" + z + "," + w + " L" + Ea + "," + st + " L" + za + "," + pr + " L" + ti + "," + et + " L" + Ds + "," + Bs + " L" + Ko + "," + rt + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star16": z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, xa = 37500 * tl, Aa = 5e4 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && "adj" == (o = Ma.attrs.name) && (xa = parseInt(Ma.attrs.fmla.substr(4)) * tl), Ds = z - (Ns = 92388 * oa / 1e5), Gs = z - (As = 70711 * oa / 1e5), js = z - ($e = 38268 * oa / 1e5), dr = z + $e, xs = z + As, Ls = z + Ns, Rs = R - (Ts = 92388 * Ca / 1e5), zs = R - (Os = 70711 * Ca / 1e5), Bs = R - (Es = 38268 * Ca / 1e5), pr = R + Es, Is = R + Os, ts = R + Ts, Ko = z - (Oa = 98079 * (Tt = oa * (Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa) / Aa) / 1e5), ti = z - (Na = 83147 * Tt / 1e5), Ea = z - (at = 55557 * Tt / 1e5), Ha = z - (Ka = 19509 * Tt / 1e5), qa = z + Ka, Ua = z + at, Va = z + Na, Qa = z + Oa, ai = R - (ut = 98079 * (At = Ca * Vs / Aa) / 1e5), ri = R - (ft = 83147 * At / 1e5), tt = R - (ht = 55557 * At / 1e5), rt = R - (ct = 19509 * At / 1e5), et = R + ct, st = R + ht, ot = R + ft, it = R + ut, vt = z - (es = Tt * Math.cos(.7853981634)), Lt = R - (os = At * Math.sin(.7853981634)), gt = z + es, bt = R + os, g += "<path   d='" + (ga = "M0," + R + " L" + Ko + "," + rt + " L" + Ds + "," + Bs + " L" + ti + "," + tt + " L" + Gs + "," + zs + " L" + Ea + "," + ri + " L" + js + "," + Rs + " L" + Ha + "," + ai + " L" + z + ",0 L" + qa + "," + ai + " L" + dr + "," + Rs + " L" + Ua + "," + ri + " L" + xs + "," + zs + " L" + Va + "," + tt + " L" + Ls + "," + Bs + " L" + Qa + "," + rt + " L" + P + "," + R + " L" + Qa + "," + et + " L" + Ls + "," + pr + " L" + Va + "," + st + " L" + xs + "," + Is + " L" + Ua + "," + ot + " L" + dr + "," + ts + " L" + qa + "," + it + " L" + z + "," + w + " L" + Ha + "," + it + " L" + js + "," + ts + " L" + Ea + "," + ot + " L" + Gs + "," + Is + " L" + ti + "," + st + " L" + Ds + "," + pr + " L" + Ko + "," + et + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star24": z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, Da = w / 4, za = P / 4, xa = 37500 * tl, Aa = 5e4 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && "adj" == (o = Ma.attrs.name) && (xa = parseInt(Ma.attrs.fmla.substr(4)) * tl), Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, Ds = z - (Ns = oa * Math.cos(.2617993878)), Gs = z - (As = oa * Math.cos(.5235987756)), js = z - ($e = oa * Math.cos(.7853981634)), dr = z - (Oe = za), xs = z - (Ve = oa * Math.cos(1.308996939)), Ls = z + Ve, vs = z + Oe, Jt = z + $e, Kt = z + As, Re = z + Ns, Rs = R - (Ts = Ca * Math.sin(1.308996939)), zs = R - (Os = Ca * Math.sin(1.0471975512)), Bs = R - (Es = Ca * Math.sin(.7853981634)), pr = R - (Hs = Da), Is = R - (qs = Ca * Math.sin(.2617993878)), ts = R + qs, as = R + Hs, er = R + Es, sr = R + Os, cr = R + Ts, Ko = z - (Oa = 99144 * (Tt = oa * Vs / Aa) / 1e5), ti = z - (Na = 92388 * Tt / 1e5), Ea = z - (at = 79335 * Tt / 1e5), Ha = z - (Ka = 60876 * Tt / 1e5), qa = z - (Ja = 38268 * Tt / 1e5), Ua = z - ($a = 13053 * Tt / 1e5), Va = z + $a, Qa = z + Ja, Xa = z + Ka, Ya = z + at, Wa = z + Na, Za = z + Oa, ai = R - (ut = 99144 * (At = Ca * Vs / Aa) / 1e5), ri = R - (ft = 92388 * At / 1e5), tt = R - (ht = 79335 * At / 1e5), rt = R - (ct = 60876 * At / 1e5), et = R - (Ba = 38268 * At / 1e5), st = R - (Fa = 13053 * At / 1e5), ot = R + Fa, it = R + Ba, nt = R + ct, lt = R + ht, dt = R + ft, pt = R + ut, vt = z - (es = Tt * Math.cos(.7853981634)), Lt = R - (os = At * Math.sin(.7853981634)), gt = z + es, bt = R + os, g += "<path   d='" + (ga = "M0," + R + " L" + Ko + "," + st + " L" + Ds + "," + Is + " L" + ti + "," + et + " L" + Gs + "," + pr + " L" + Ea + "," + rt + " L" + js + "," + Bs + " L" + Ha + "," + tt + " L" + dr + "," + zs + " L" + qa + "," + ri + " L" + xs + "," + Rs + " L" + Ua + "," + ai + " L" + z + ",0 L" + Va + "," + ai + " L" + Ls + "," + Rs + " L" + Qa + "," + ri + " L" + vs + "," + zs + " L" + Xa + "," + tt + " L" + Jt + "," + Bs + " L" + Ya + "," + rt + " L" + Kt + "," + pr + " L" + Wa + "," + et + " L" + Re + "," + Is + " L" + Za + "," + st + " L" + P + "," + R + " L" + Za + "," + ot + " L" + Re + "," + ts + " L" + Wa + "," + it + " L" + Kt + "," + as + " L" + Ya + "," + nt + " L" + Jt + "," + er + " L" + Xa + "," + lt + " L" + vs + "," + sr + " L" + Qa + "," + dt + " L" + Ls + "," + cr + " L" + Va + "," + pt + " L" + z + "," + w + " L" + Ua + "," + pt + " L" + xs + "," + cr + " L" + qa + "," + dt + " L" + dr + "," + sr + " L" + Ha + "," + lt + " L" + js + "," + er + " L" + Ea + "," + nt + " L" + Gs + "," + as + " L" + ti + "," + it + " L" + Ds + "," + ts + " L" + Ko + "," + ot + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "star32": var Ba, Fa, Na, Oa, Ea, Ha, qa, Ua, Va, Qa, Xa, Ya, Wa, Za, $a, Ja, Ka, at, tt, rt, et, st, ot, it, nt, lt, dt, pt, ct, ht, ft, ut, vt, Lt, gt, bt, z = P / 2, R = w / 2, oa = P / 2, Ca = w / 2, Da = w / 4, za = P / 4, xa = 37500 * tl, Aa = 5e4 * tl; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && "adj" == (o = Ma.attrs.name) && (xa = parseInt(Ma.attrs.fmla.substr(4)) * tl), Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, Ds = z - (Ns = 98079 * oa / 1e5), Gs = z - (As = 92388 * oa / 1e5), js = z - ($e = 83147 * oa / 1e5), dr = z - (Oe = oa * Math.cos(.7853981634)), xs = z - (Ve = 55557 * oa / 1e5), Ls = z - (br = 38268 * oa / 1e5), vs = z - (ks = 19509 * oa / 1e5), Jt = z + ks, Kt = z + br, Re = z + Ve, Te = z + Oe, Ge = z + $e, De = z + As, Ae = z + Ns, Rs = R - (Ts = 98079 * Ca / 1e5), zs = R - (Os = 92388 * Ca / 1e5), Bs = R - (Es = 83147 * Ca / 1e5), pr = R - (Hs = Ca * Math.sin(.7853981634)), Is = R - (qs = 55557 * Ca / 1e5), ts = R - (Da = 38268 * Ca / 1e5), as = R - (za = 19509 * Ca / 1e5), er = R + za, sr = R + Da, cr = R + qs, kr = R + Hs, gr = R + Es, yr = R + Os, br = R + Ts, Ko = z - (Oa = 99518 * (Tt = oa * Vs / Aa) / 1e5), ti = z - (Na = 95694 * Tt / 1e5), Ea = z - (at = 88192 * Tt / 1e5), Ha = z - (Ka = 77301 * Tt / 1e5), qa = z - (Ja = 63439 * Tt / 1e5), Ua = z - ($a = 47140 * Tt / 1e5), Va = z - (za = 29028 * Tt / 1e5), Qa = z - (Da = 9802 * Tt / 1e5), Xa = z + Da, Ya = z + za, Wa = z + $a, Za = z + Ja, $a = z + Ka, Ja = z + at, Ka = z + Na, at = z + Oa, ai = R - (ut = 99518 * (At = Ca * Vs / Aa) / 1e5), ri = R - (ft = 95694 * At / 1e5), tt = R - (ht = 88192 * At / 1e5), rt = R - (ct = 77301 * At / 1e5), et = R - (Ba = 63439 * At / 1e5), st = R - (Fa = 47140 * At / 1e5), ot = R - (Na = 29028 * At / 1e5), it = R - (Oa = 9802 * At / 1e5), nt = R + Oa, lt = R + Na, dt = R + Fa, pt = R + Ba, ct = R + ct, ht = R + ht, ft = R + ft, ut = R + ut, vt = z - (es = Tt * Math.cos(.7853981634)), Lt = R - (os = At * Math.sin(.7853981634)), gt = z + es, bt = R + os, g += "<path   d='" + (ga = "M0," + R + " L" + Ko + "," + it + " L" + Ds + "," + as + " L" + ti + "," + ot + " L" + Gs + "," + ts + " L" + Ea + "," + st + " L" + js + "," + Is + " L" + Ha + "," + et + " L" + dr + "," + pr + " L" + qa + "," + rt + " L" + xs + "," + Bs + " L" + Ua + "," + tt + " L" + Ls + "," + zs + " L" + Va + "," + ri + " L" + vs + "," + Rs + " L" + Qa + "," + ai + " L" + z + ",0 L" + Xa + "," + ai + " L" + Jt + "," + Rs + " L" + Ya + "," + ri + " L" + Kt + "," + zs + " L" + Wa + "," + tt + " L" + Re + "," + Bs + " L" + Za + "," + rt + " L" + Te + "," + pr + " L" + $a + "," + et + " L" + Ge + "," + Is + " L" + Ja + "," + st + " L" + De + "," + ts + " L" + Ka + "," + ot + " L" + Ae + "," + as + " L" + at + "," + it + " L" + P + "," + R + " L" + at + "," + nt + " L" + Ae + "," + er + " L" + Ka + "," + lt + " L" + De + "," + sr + " L" + Ja + "," + dt + " L" + Ge + "," + cr + " L" + $a + "," + pt + " L" + Te + "," + kr + " L" + Za + "," + ct + " L" + Re + "," + gr + " L" + Wa + "," + ht + " L" + Kt + "," + yr + " L" + Ya + "," + ft + " L" + Jt + "," + br + " L" + Xa + "," + ut + " L" + z + "," + w + " L" + Qa + "," + ut + " L" + vs + "," + br + " L" + Va + "," + ft + " L" + Ls + "," + yr + " L" + Ua + "," + ht + " L" + xs + "," + gr + " L" + qa + "," + ct + " L" + dr + "," + kr + " L" + Ha + "," + pt + " L" + js + "," + cr + " L" + Ea + "," + dt + " L" + Gs + "," + sr + " L" + ti + "," + lt + " L" + Ds + "," + er + " L" + Ko + "," + nt + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "pie": case "pieWedge": case "arc": "pie" == m ? (wt = 270, mt = w, al = !(Sa = 0)) : "pieWedge" == m ? (Sa = 180, wt = 270, mt = 2 * w, al = !0) : "arc" == m && (wt = 0, mt = w, al = !(Sa = 270)), void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && (void 0 === (nt = lt = ul(Ma, ["attrs", "fmla"])) && (lt = Ma[0].attrs.fmla, nt = Ma[1].attrs.fmla), void 0 !== lt && (Sa = parseInt(lt.substr(4)) / 6e4), void 0 !== nt && (wt = parseInt(nt.substr(4)) / 6e4)); var mt = function (a, t, r, e, s) { var o = parseInt(e), i = parseInt(r), e = parseInt(a) / 2, r = o - i; r < 0 && (r = 360 + r); r = Math.min(Math.max(r, 0), 360); var n, a = Math.cos(2 * Math.PI / (360 / r)), o = Math.sin(2 * Math.PI / (360 / r)); e = s ? (n = "M" + e + "," + e + " L" + e + ",0 A" + e + "," + e + " 0 " + (r <= 180 ? 0 : 1) + ",1 " + (e + o * e) + "," + (e - a * e) + " z", "rotate(" + (i - 270) + ", " + e + ", " + e + ")") : (n = "M" + e + ",0 A" + (t = t / 2) + "," + e + " 0 " + (r <= 180 ? 0 : 1) + ",1 " + (t + o * t) + "," + (e - a * e), "rotate(" + (i + 90) + ", " + e + ", " + e + ")"); return [n, e] }(mt, P, Sa, wt, al); g += "<path   d='" + mt[0] + "' transform='" + mt[1] + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "chord": va = 45, La = 270; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = parseInt(Zs.substr(4)) / 6e4) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 6e4); g += "<path d='" + (zn = sl(Zn = P / 2, Wn = w / 2, Zn, Wn, va, La, !0)) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "frame": var Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), Sa = 12500 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; void 0 !== Ma && (Sa = parseInt(Ma.substr(4)) * tl), eo = Sa < 0 ? 0 : wa < Sa ? wa : Sa, g += "<path   d='" + (ga = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " zM" + (Ds = Math.min(P, w) * eo / Ia) + "," + Ds + " L" + Ds + "," + (pr = w - Ds) + " L" + (dr = P - Ds) + "," + pr + " L" + dr + "," + Ds + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "donut": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl), Vs = xa < 0 ? 0 : wa < xa ? wa : xa, Tt = P / 2 - (St = Math.min(P, w) * Vs / Ia), At = w / 2 - St, g += "<path   d='" + (ga = "M0," + w / 2 + sl(P / 2, w / 2, P / 2, w / 2, 180, 270, !1).replace("M", "L") + sl(P / 2, w / 2, P / 2, w / 2, 270, 360, !1).replace("M", "L") + sl(P / 2, w / 2, P / 2, w / 2, 0, 90, !1).replace("M", "L") + sl(P / 2, w / 2, P / 2, w / 2, 90, 180, !1).replace("M", "L") + " zM" + St + "," + w / 2 + sl(P / 2, w / 2, Tt, At, 180, 90, !1).replace("M", "L") + sl(P / 2, w / 2, Tt, At, 90, 0, !1).replace("M", "L") + sl(P / 2, w / 2, Tt, At, 0, -90, !1).replace("M", "L") + sl(P / 2, w / 2, Tt, At, 270, 180, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "noSmoking": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 18750 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl), Vs = xa < 0 ? 0 : wa < xa ? wa : xa, Tt = P / 2 - (St = Math.min(P, w) * Vs / Ia), At = w / 2 - St, yt = Math.atan(w / P), mt = At * Math.cos(yt), cs = Tt * Math.sin(yt), hs = Tt * At / Math.sqrt(mt * mt + cs * cs), kt = St / 2, cs = 2 * (mt = Math.atan(kt / hs)), Jn = -Math.PI + cs, hs = (kt = yt - mt) - Math.PI, yt = At * Math.cos(kt), mt = Tt * Math.sin(kt), Ds = P / 2 + (Ns = (yt = Tt * At / Math.sqrt(yt * yt + mt * mt)) * Math.cos(kt)), Rs = w / 2 + (Ts = yt * Math.sin(kt)), Gs = P / 2 - Ns, zs = w / 2 - Ts; var mt = 180 * kt / Math.PI, yt = 180 * hs / Math.PI, kt = 180 * Jn / Math.PI; g += "<path   d='" + (ga = "M0," + w / 2 + sl(P / 2, w / 2, P / 2, w / 2, 180, 270, !1).replace("M", "L") + sl(P / 2, w / 2, P / 2, w / 2, 270, 360, !1).replace("M", "L") + sl(P / 2, w / 2, P / 2, w / 2, 0, 90, !1).replace("M", "L") + sl(P / 2, w / 2, P / 2, w / 2, 90, 180, !1).replace("M", "L") + " zM" + Ds + "," + Rs + sl(P / 2, w / 2, Tt, At, mt, mt + kt, !1).replace("M", "L") + " zM" + Gs + "," + zs + sl(P / 2, w / 2, Tt, At, yt, yt + kt, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "halfFrame": var ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), va = 3.5, La = 3.5, Mt = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) * tl); var xt = Mt * P / (Ye = Math.min(P, w)), Pt = Mt * (Mr = w - (Lr = w * (Ds = Ye * (zo = La < 0 ? 0 : xt < La ? xt : La) / Mt) / P)) / Ye; g += "<path   d='" + (ga = "M0,0 L" + P + ",0 L" + (Gs = P - (As = (Rs = Ye * (eo = va < 0 ? 0 : Pt < va ? Pt : va) / Mt) * P / w)) + "," + Rs + " L" + Ds + "," + Rs + " L" + Ds + "," + (zs = w - (Os = Ds * w / P)) + " L0," + w + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "blockArc": var ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 180, wt = 0, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) / 6e4) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) / 6e4) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); ws = It < 0 ? 0 : wa < It ? wa : It; var _t, Ct, St, Tt, At, Dt, Gt, jt = ($n = Sa < 0 ? 0 : 360 < Sa ? 360 : Sa) + (Jn = 0 < (yt = (Mn = wt < 0 ? 0 : 360 < wt ? 360 : wt) - $n) ? yt : 360 + yt), Rt = Mn + (xn = -Jn), kt = $n * Math.PI / 180, yt = Mn * Math.PI / 180; z = oa = P / 2, R = Ca = w / 2, Rs = 90 < $n && $n < 270 ? (_t = oa * Math.sin(Math.PI / 2 - kt), Ct = Ca * Math.cos(Math.PI / 2 - kt), Ds = z - (Ns = oa * Math.cos(Math.atan(Ct / _t))), R - (Ts = Ca * Math.sin(Math.atan(Ct / _t)))) : (_t = oa * Math.sin(kt), Ct = Ca * Math.cos(kt), Ds = z + (Ns = oa * Math.cos(Math.atan(_t / Ct))), R + (Ts = Ca * Math.sin(Math.atan(_t / Ct)))), Tt = oa - (St = Math.min(P, w) * ws / Ia), At = Ca - St, zs = jt <= 450 && 270 < jt || 630 <= jt && jt < 720 ? (Dt = Tt * Math.sin(yt), Gt = At * Math.cos(yt), Gs = z + (As = Tt * Math.cos(Math.atan(Dt / Gt))), R + (Os = At * Math.sin(Math.atan(Dt / Gt)))) : (Dt = Tt * Math.sin(Math.PI / 2 - yt), Gt = At * Math.cos(Math.PI / 2 - yt), Gs = z - (As = Tt * Math.cos(Math.atan(Gt / Dt))), R - (Os = At * Math.sin(Math.atan(Gt / Dt)))), g += "<path   d='" + (ga = "M" + Ds + "," + Rs + sl(oa, Ca, oa, Ca, $n, jt, !1).replace("M", "L") + " L" + Gs + "," + zs + sl(oa, Ca, Tt, At, Mn, Rt, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "bracePair": var Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 8333 * tl, wa = 25e3 * tl, Ia = 5e4 * tl, zt = 1e5 * tl; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); var R = w / 2, Bt = 360, ia = 180, sa = 90, na = 270; Vs = xa < 0 ? 0 : wa < xa ? wa : xa, js = P - (Gs = (Ye = Math.min(P, w)) * Vs / Ia), dr = P - (Ds = Ye * Vs / zt), zs = R - Ds, Bs = R + Ds, g += "<path   d='" + (ga = "M" + Gs + "," + w + sl(Gs, pr = w - Ds, Ds, Ds, sa, ia, !1).replace("M", "L") + " L" + Ds + "," + Bs + sl(0, Bs, Ds, Ds, 0, -sa, !1).replace("M", "L") + sl(0, zs, Ds, Ds, sa, 0, !1).replace("M", "L") + " L" + Ds + "," + Ds + sl(Gs, Ds, Ds, Ds, ia, na, !1).replace("M", "L") + " M" + js + ",0" + sl(js, Ds, Ds, Ds, na, Bt, !1).replace("M", "L") + " L" + dr + "," + zs + sl(P, zs, Ds, Ds, ia, sa, !1).replace("M", "L") + sl(P, Bs, Ds, Ds, na, ia, !1).replace("M", "L") + " L" + dr + "," + pr + sl(js, pr, Ds, Ds, 0, sa, !1).replace("M", "L")) + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftBrace": var ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 8333 * tl, wt = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl); R = w / 2, ia = 180, sa = 90, na = 270, Pt = (hi = (ci = Ia - (zo = wt < 0 ? 0 : Ia < wt ? Ia : wt)) < zo ? ci : zo) / 2 * w / (Ye = Math.min(P, w)); zs = (Bs = w * zo / Ia) - (Rs = Ye * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / Ia), pr = Bs + Rs, g += "<path   d='" + (ga = "M" + P + "," + w + sl(P, w - Rs, P / 2, Rs, sa, ia, !1).replace("M", "L") + " L" + P / 2 + "," + pr + sl(0, pr, P / 2, Rs, 0, -sa, !1).replace("M", "L") + sl(0, zs, P / 2, Rs, sa, 0, !1).replace("M", "L") + " L" + P / 2 + "," + Rs + sl(P, Rs, P / 2, Rs, ia, na, !1).replace("M", "L")) + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "rightBrace": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 8333 * tl, wt = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl); R = w / 2, Bt = 360, ia = 180, sa = 90, na = 270, Pt = (hi = (ci = Ia - (zo = wt < 0 ? 0 : Ia < wt ? Ia : wt)) < zo ? ci : zo) / 2 * w / (Ye = Math.min(P, w)); zs = (Bs = w * zo / Ia) - (Rs = Ye * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / Ia), pr = w - Rs, g += "<path   d='" + (ga = "M0,0" + sl(0, Rs, P / 2, Rs, na, Bt, !1).replace("M", "L") + " L" + P / 2 + "," + zs + sl(P, zs, P / 2, Rs, ia, sa, !1).replace("M", "L") + sl(P, Bs + Rs, P / 2, Rs, na, ia, !1).replace("M", "L") + " L" + P / 2 + "," + pr + sl(0, pr, P / 2, Rs, 0, sa, !1).replace("M", "L")) + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "bracketPair": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 16667 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); var Ft = w, ia = 180, sa = 90, na = 270; Vs = xa < 0 ? 0 : wa < xa ? wa : xa, Gs = (Nt = P) - (Ds = Math.min(P, w) * Vs / Ia), zs = Ft - Ds, g += "<path   d='" + (ga = sl(Ds, Ds, Ds, Ds, na, ia, !1) + sl(Ds, zs, Ds, Ds, ia, sa, !1).replace("M", "L") + sl(Gs, Ds, Ds, Ds, na, na + sa, !1) + sl(Gs, zs, Ds, Ds, 0, sa, !1).replace("M", "L")) + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftBracket": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 8333 * tl, Ia = 1e5 * tl, Aa = (wa = 5e4 * tl) * w / Math.min(P, w); void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); var Nt = P, Ft = w, ia = 180, sa = 90, na = 270; Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, P < (Rs = Math.min(P, w) * Vs / Ia) && (Rs = P), g += "<path   d='" + (ga = "M" + Nt + "," + Ft + sl(Rs, zs = Ft - Rs, Rs, Rs, sa, ia, !1).replace("M", "L") + " L0," + Rs + sl(Rs, Rs, Rs, Rs, ia, na, !1).replace("M", "L") + " L" + Nt + ",0") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "rightBracket": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 8333 * tl, Ia = 1e5 * tl, Aa = (wa = 5e4 * tl) * w / Math.min(P, w); void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); Bt = 360, ia = 180, sa = 90, na = 270; Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa, g += "<path   d='" + (ga = "M0," + w + sl(Bs = P - (Rs = Math.min(P, w) * Vs / Ia), zs = w - Rs, Rs, Rs, sa, 0, !1).replace("M", "L") + " L" + P + "," + w / 2 + sl(Bs, Rs, Rs, Rs, Bt, na, !1).replace("M", "L") + " L0,0") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "moon": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = .5; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) / 1e5); wt = (1 - xa) * P; g += "<path   d='" + (ga = "M" + P + "," + w + sl(P, Ca = w / 2, P, Ca, sa = 90, sa + (ia = 180), !1).replace("M", "L") + sl(P, Ca, wt, Ca, sa + ia, sa, !1).replace("M", "L") + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "corner": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), va = 5e4 * tl, La = 5e4 * tl, Mt = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) * tl); Pt = Mt * w / (Ye = Math.min(P, w)), xt = Mt * P / Ye; g += "<path   d='" + (ga = "M0,0 L" + (Ds = Ye * (zo = La < 0 ? 0 : xt < La ? xt : La) / Mt) + ",0 L" + Ds + "," + (Rs = w - (Ts = Ye * (eo = va < 0 ? 0 : Pt < va ? Pt : va) / Mt)) + " L" + P + "," + Rs + " L" + P + "," + w + " L0," + w + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "diagStripe": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), va = 5e4 * tl, Mt = 1e5 * tl; void 0 !== Ma && (va = parseInt(Ma.substr(4)) * tl), g += "<path   d='" + (ga = "M0," + (zs = w * (eo = va < 0 ? 0 : Mt < va ? Mt : va) / Mt) + " L" + (Gs = P * eo / Mt) + ",0 L" + P + ",0 L0," + w + " z") + "'  fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "gear6": case "gear9": x = 0; var Ot = m.substr(4); g += "<path   d='" + (ga = el(0, w / 3.5, parseInt(Ot))) + "' transform='rotate(20," + 3 / 7 * w + "," + 3 / 7 * w + ")' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "bentConnector3": ba = .5; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (g += " <polyline points='0 0," + (ba = parseInt(Ma.substr(4)) / 1e5) * P + " 0," + ba * P + " " + w + "," + P + " " + w + "' fill='transparent'' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' ", void 0 === T || "triangle" !== T.type && "arrow" !== T.type || (g += "marker-start='url(#markerTriangle_" + b + ")' "), void 0 === A || "triangle" !== A.type && "arrow" !== A.type || (g += "marker-end='url(#markerTriangle_" + b + ")' "), g += "/>"); break; case "plus": Sa = .25; void 0 !== (Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Sa = parseInt(Ma.substr(4)) / 1e5), g += " <polygon points='" + Sa * P + " 0," + Sa * P + " " + Sa * w + ",0 " + Sa * w + ",0 " + (wt = 1 - Sa) * w + "," + Sa * P + " " + wt * w + "," + Sa * P + " " + w + "," + wt * P + " " + w + "," + wt * P + " " + wt * w + "," + P + " " + wt * w + "," + +P + " " + Sa * w + "," + wt * P + " " + Sa * w + "," + wt * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "teardrop": var Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), Et = Sa = 1e5 * tl, Ht = 2e5 * tl; void 0 !== Ma && (Sa = parseInt(Ma.substr(4)) * tl), eo = Sa < 0 ? 0 : Ht < Sa ? Ht : Sa, fr = (hr = Math.sqrt(2)) * (P / 2) * eo / Et, ur = (oo = hr * (w / 2)) * eo / Et, vr = 45 * Math.PI / 180, Gs = (P / 2 + (Ds = P / 2 + (Ns = fr * Math.cos(vr)))) / 2, zs = (w / 2 + (Rs = w / 2 - (Ts = ur * Math.cos(vr)))) / 2, g += "<path   d='" + (zn = sl(P / 2, w / 2, P / 2, w / 2, 180, 270, !1) + "Q " + Gs + ",0 " + Ds + "," + Rs + "Q " + P + "," + zs + " " + P + "," + w / 2 + sl(P / 2, w / 2, P / 2, w / 2, 0, 90, !1).replace("M", "L") + sl(P / 2, w / 2, P / 2, w / 2, 90, 180, !1).replace("M", "L") + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "plaque": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), Sa = 16667 * tl, Et = 5e4 * tl, Ht = 1e5 * tl; void 0 !== Ma && (Sa = parseInt(Ma.substr(4)) * tl), Gs = P - (Ds = (eo = Sa < 0 ? 0 : Et < Sa ? Et : Sa) * Math.min(P, w) / Ht), zs = w - Ds, g += "<path   d='" + (zn = "M0," + Ds + sl(0, 0, Ds, Ds, 90, 0, !1).replace("M", "L") + " L" + Gs + ",0" + sl(P, 0, Ds, Ds, 180, 90, !1).replace("M", "L") + " L" + P + "," + zs + sl(P, w, Ds, Ds, 270, 180, !1).replace("M", "L") + " L" + Ds + "," + w + sl(0, w, Ds, Ds, 0, -90, !1).replace("M", "L") + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "sun": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), Sa = 25e3 * (Fs = tl), wa = 12500 * Fs, Ia = 46875 * Fs; void 0 !== Ma && (Sa = parseInt(Ma.substr(4)) * Fs); var qt = 5e4 * Fs, Ut = 1e5 * Fs, Vt = qt - (eo = Sa < wa ? wa : Ia < Sa ? Ia : Sa), Qt = (Lr = 30274 * Fs * Vt / (32768 * Fs)) + qt, Xt = (Mr = 12540 * Fs * Vt / (32768 * Fs)) + qt, Yt = P * (18436 * Fs) / (21600 * Fs), Wt = w * (3163 * Fs) / (21600 * Fs), Zt = P * (3163 * Fs) / (21600 * Fs), $t = w * (18436 * Fs) / (21600 * Fs), Jt = P * (Ir = qt + (wr = 23170 * Fs * Vt / (32768 * Fs))) / Ut, Kt = P * (_r = qt - wr) / Ut, ar = P * (Or = Ut - (Yr = (Cr = 3 * (xr = qt - Lr) / 4) + 3662 * Fs)) / Ut, tr = P * (Gr = Ut - (Tr = (Sr = 3 * (Pr = qt - Mr) / 4) + 36620 * Fs)) / Ut, rr = P * (jr = Ut - (Ar = Sr + 12500 * Fs)) / Ut, er = w * Ir / Ut, sr = w * _r / Ut, or = w * (Dr = Ut - Cr) / Ut, ir = w * Or / Ut, nr = w * Gr / Ut, lr = w * jr / Ut; g += "<path   d='" + (zn = "M" + P + "," + w / 2 + " L" + (_e = P * Dr / Ut) + "," + lr + " L" + _e + "," + (br = w * Ar / Ut) + "z M" + Yt + "," + Wt + " L" + ar + "," + nr + " L" + (De = P * Tr / Ut) + "," + (gr = w * Yr / Ut) + "z M" + P / 2 + ",0 L" + rr + "," + (cr = w * Cr / Ut) + " L" + (Ae = P * Ar / Ut) + "," + cr + "z M" + Zt + "," + Wt + " L" + tr + "," + gr + " L" + (Ge = P * Yr / Ut) + "," + nr + "z M0," + w / 2 + " L" + (Re = P * Cr / Ut) + "," + br + " L" + Re + "," + lr + "z M" + Zt + "," + $t + " L" + Ge + "," + (yr = w * Tr / Ut) + " L" + tr + "," + ir + "z M" + P / 2 + "," + w + " L" + Ae + "," + or + " L" + rr + "," + or + "z M" + Yt + "," + $t + " L" + De + "," + ir + " L" + ar + "," + yr + " z M" + P * eo / Ut + "," + w / 2 + sl(P / 2, w / 2, Zn = P * Vt / Ut, Wn = w * Vt / Ut, 180, 540, !1).replace("M", "L") + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "heart": g += "<path   d='" + (zn = "M" + P / 2 + "," + w / 4 + "C" + (js = P / 2 + (As = 10 * P / 48)) + "," + (Rs = -w / 3) + " " + (dr = P / 2 + (Ns = 49 * P / 48)) + "," + w / 4 + " " + P / 2 + "," + w + "C" + (Ds = P / 2 - Ns) + "," + w / 4 + " " + (Gs = P / 2 - As) + "," + Rs + " " + P / 2 + "," + w / 4 + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "lightningBolt": var dr = 8757 * P / 21600, Kt = 13917 * P / 21600, pr = 7437 * w / 21600, cr = 14277 * w / 21600; g += "<path d='" + (zn = "M" + (js = 8472 * P / 21600) + ",0 L" + (Jt = 12860 * P / 21600) + "," + (zs = 6080 * w / 21600) + " L" + (Gs = 11050 * P / 21600) + "," + (Bs = 6797 * w / 21600) + " L" + (Te = 16577 * P / 21600) + "," + (as = 12007 * w / 21600) + " L" + (Ls = 14767 * P / 21600) + "," + (Is = 12877 * w / 21600) + " L" + P + "," + w + " L" + (xs = 10012 * P / 21600) + "," + (kr = 14915 * w / 21600) + " L" + (vs = 12222 * P / 21600) + "," + (er = 13987 * w / 21600) + " L" + (Ds = 5022 * P / 21600) + "," + (ts = 9705 * w / 21600) + " L" + (Re = 7602 * P / 21600) + "," + (sr = 8382 * w / 21600) + " L0," + (Rs = 3890 * w / 21600) + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "cube": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 25e3 * (Fs = tl); void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * Fs); Ia = 1e5 * Fs; g += "<path d='" + (zn = "M0," + (Rs = (Hr = Math.min(P, w)) * (Vs = xa < 0 ? 0 : Ia < xa ? Ia : xa) / Ia) + " L" + Rs + ",0 L" + P + ",0 L" + P + "," + (pr = w - Rs) + " L" + (dr = P - Rs) + "," + w + " L0," + w + " zM0," + Rs + " L" + dr + "," + Rs + " M" + dr + "," + Rs + " L" + P + ",0M" + dr + "," + Rs + " L" + dr + "," + w) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "bevel": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 12500 * (Fs = tl); void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * Fs); wa = 5e4 * Fs, Ia = 1e5 * Fs; g += "<path d='" + (zn = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Ds = (Hr = Math.min(P, w)) * (Vs = xa < 0 ? 0 : wa < xa ? wa : xa) / Ia) + "," + Ds + " L" + (Gs = P - Ds) + "," + Ds + " L" + Gs + "," + (zs = w - Ds) + " L" + Ds + "," + zs + " z M0,0 L" + Ds + "," + Ds + " M0," + w + " L" + Ds + "," + zs + " M" + P + ",0 L" + Gs + "," + Ds + " M" + P + "," + w + " L" + Gs + "," + zs) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "foldedCorner": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 16667 * (Fs = tl); void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * Fs); wa = 5e4 * Fs, Ia = 1e5 * Fs; g += "<path d='" + (zn = "M" + (Ds = P - (Os = (Hr = Math.min(P, w)) * (Vs = xa < 0 ? 0 : wa < xa ? wa : xa) / Ia)) + "," + w + " L" + (Gs = Ds + (Ts = Os / 5)) + "," + (Rs = (zs = w - Os) + Ts) + " L" + P + "," + zs + " L" + Ds + "," + w + " L0," + w + " L0,0 L" + P + ",0 L" + P + "," + zs) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "cloud": case "cloudCallout": Rt = 3900 * P / 43200; Ds = 4693 * P / 43200, Gs = 6928 * P / 43200, js = 16478 * P / 43200, dr = 28827 * P / 43200, xs = 34129 * P / 43200, Ls = 41798 * P / 43200, vs = 38324 * P / 43200, Jt = 29078 * P / 43200, Kt = 22141 * P / 43200, Re = 14e3 * P / 43200, Te = 4127 * P / 43200, Bt = 14370 * w / 43200, Rs = 26177 * w / 43200, zs = 34899 * w / 43200, Bs = 39090 * w / 43200, pr = 34751 * w / 43200, Is = 22954 * w / 43200, ts = 15354 * w / 43200, as = 5426 * w / 43200, er = 3952 * w / 43200, sr = 4720 * w / 43200, cr = 5192 * w / 43200, kr = 15789 * w / 43200; var Mt = 6753 * P / 43200, Ot = 9190 * w / 43200, ba = 5333 * P / 43200, hr = 7267 * w / 43200, fr = 4365 * P / 43200, ur = 5945 * w / 43200, vr = 4857 * P / 43200, Et = 6595 * w / 43200, Ht = 7273 * w / 43200, Lr = 6775 * P / 43200, qt = 9220 * w / 43200, Wt = 5785 * P / 43200, gr = 7867 * w / 43200, nr = 6752 * P / 43200, br = 9215 * w / 43200, lr = 7720 * P / 43200, Zt = 10543 * w / 43200, tr = 4360 * P / 43200, rr = 5918 * w / 43200, mr = 4345 * P / 43200, or = -11429249 / 6e4, Yt = -8646143 / 6e4, $t = -8748475 / 6e4, ir = -7859164 / 6e4, ar = -4722533 / 6e4, yr = -46.26725, Vt = 37501 / 6e4, Ut = 22.4516, sr = 3974558 / 6e4, cr = -16496525 / 6e4, kr = -246.8285, Ot = sl(Rt - Mt * Math.cos(or * Math.PI / 180), Bt - Ot * Math.sin(or * Math.PI / 180), Mt, Ot, or, or + 7426832 / 6e4, !1).replace("M", "L"), or = Ot.substr(Ot.lastIndexOf("L") + 1).split(" "), Yt = (hr = sl(qr = parseInt(or[0]) - ba * Math.cos(Yt * Math.PI / 180), Ur = parseInt(or[1]) - hr * Math.sin(Yt * Math.PI / 180), ba, hr, Yt, -54.15715, !1).replace("M", "L")).substr(hr.lastIndexOf("L") + 1).split(" "), $t = (fr = sl(B = parseInt(Yt[0]) - fr * Math.cos($t * Math.PI / 180), O = parseInt(Yt[1]) - ur * Math.sin($t * Math.PI / 180), fr, ur, $t, $t + 5983381 / 6e4, !1).replace("M", "L")).substr(fr.lastIndexOf("L") + 1).split(" "), ir = (Et = sl(ra = parseInt($t[0]) - vr * Math.cos(ir * Math.PI / 180), F = parseInt($t[1]) - Et * Math.sin(ir * Math.PI / 180), vr, Et, ir, ir + 7034504 / 6e4, !1).replace("M", "L")).substr(Et.lastIndexOf("L") + 1).split(" "), ar = (Ht = sl(N = parseInt(ir[0]) - ba * Math.cos(ar * Math.PI / 180), ea = parseInt(ir[1]) - Ht * Math.sin(ar * Math.PI / 180), ba, Ht, ar, ar + 6541615 / 6e4, !1).replace("M", "L")).substr(Ht.lastIndexOf("L") + 1).split(" "), yr = (qt = sl(parseInt(ar[0]) - Lr * Math.cos(yr * Math.PI / 180), parseInt(ar[1]) - qt * Math.sin(yr * Math.PI / 180), Lr, qt, yr, 130.269 + yr, !1).replace("M", "L")).substr(qt.lastIndexOf("L") + 1).split(" "), Vt = (gr = sl(parseInt(yr[0]) - Wt * Math.cos(Vt * Math.PI / 180), parseInt(yr[1]) - gr * Math.sin(Vt * Math.PI / 180), Wt, gr, Vt, 114.65835, !1).replace("M", "L")).substr(gr.lastIndexOf("L") + 1).split(" "), Ut = (br = sl(parseInt(Vt[0]) - nr * Math.cos(Ut * Math.PI / 180), parseInt(Vt[1]) - br * Math.sin(Ut * Math.PI / 180), nr, br, Ut, 137.62415, !1).replace("M", "L")).substr(br.lastIndexOf("L") + 1).split(" "), sr = (Zt = sl(parseInt(Ut[0]) - lr * Math.cos(sr * Math.PI / 180), parseInt(Ut[1]) - Zt * Math.sin(sr * Math.PI / 180), lr, Zt, sr, sr + 4542661 / 6e4, !1).replace("M", "L")).substr(Zt.lastIndexOf("L") + 1).split(" "), cr = (rr = sl(parseInt(sr[0]) - tr * Math.cos(cr * Math.PI / 180), parseInt(sr[1]) - rr * Math.sin(cr * Math.PI / 180), tr, rr, cr, cr + 8804134 / 6e4, !1).replace("M", "L")).substr(rr.lastIndexOf("L") + 1).split(" "), mr = "M" + Rt + "," + Bt + Ot + hr + fr + Et + Ht + qt + gr + br + Zt + rr + sl(parseInt(cr[0]) - mr * Math.cos(kr * Math.PI / 180), parseInt(cr[1]) - ur * Math.sin(kr * Math.PI / 180), mr, ur, kr, -94.30965, !1).replace("M", "L") + " z"; if ("cloudCallout" == m) { ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = -20833 * (Fs = tl), wt = 62500 * Fs; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * Fs) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * Fs); var Mr, xr, Pr, wr, Ir, _r, Cr, Sr, Tr, Ar, Dr, Gr, jr, Rr, zr, Br, Fr, Nr, Or, Er, Ia = 1e5 * Fs, Hr = Math.min(P, w); Kr = (oa = P / 2) + ($r = P * Sa / Ia), ae = (Ca = w / 2) + (Jr = w * wt / Ia), ur = Ca * Math.cos(Math.atan(Jr / $r)), kr = oa * Math.sin(Math.atan(Jr / $r)), Mr = oa * Math.cos(Math.atan(kr / ur)), Qt = Ca * Math.sin(Math.atan(kr / ur)), xr = 0 <= Sa ? (Xt = oa + Mr, Ca + Qt) : (Xt = oa - Mr, Ca - Qt), Pr = Xt - Kr, wr = xr - ae, Gr = (Dr = (Tr = (Sr = (Cr = (Ir = Math.sqrt(Pr * Pr + wr * wr)) - (_r = 6600 * Hr / 21600)) / 3) + (Yr = 1800 * Hr / 21600)) * wr / Ir) + ae, Vr = (Br = (zr = (jr = 4800 * Hr / 21600) + (Rr = 2 * Sr)) * wr / Ir) + ae, Or = (Or = (Ar = Tr * Pr / Ir) + Kr) + (Fr = 1200 * Hr / 21600), Er = (Er = zr * Pr / Ir) + Kr + Yr, mr += zn = sl(Kr + (Nr = 600 * Hr / 21600) - Nr, ae, Nr, Nr, 0, 360, !1) + " z M" + Or + "," + Gr + sl(Or - Fr, Gr, Fr, Fr, 0, 360, !1).replace("M", "L") + " z M" + Er + "," + Vr + sl(Er - Yr, Vr, Yr, Yr, 0, 360, !1).replace("M", "L") + " z" } g += "<path d='" + mr + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "smileyFace": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 4653 * (Fs = tl); void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * Fs); wa = 5e4 * Fs, Ia = 1e5 * Fs, zt = 4653 * Fs, Hr = Math.min(P, w); oa = P / 2, Ca = w / 2, Ds = 4969 * P / 21699, js = 13135 * P / 21600, dr = 16640 * P / 21600, Rs = 7570 * w / 21600, zs = (Bs = 16515 * w / 21600) - (Os = w * (Vs = xa < -zt ? -zt : zt < xa ? zt : xa) / Ia), Is = (pr = Bs + Os) + (Es = w * Vs / wa), Wn = 1125 * w / 21600; var qr = (Gs = 6215 * P / 21600) - (Zn = 1125 * P / 21600) * Math.cos(Math.PI), Ur = Rs - Wn * Math.sin(Math.PI), B = js - Zn * Math.cos(Math.PI); g += "<path d='" + (zn = sl(qr, Ur, Zn, Wn, 180, 540, !1) + sl(B, Ur, Zn, Wn, 180, 540, !1) + " M" + Ds + "," + zs + " Q" + oa + "," + Is + " " + dr + "," + zs + " Q" + oa + "," + Is + " " + Ds + "," + zs + " M0," + Ca + sl(oa, Ca, oa, Ca, 180, 540, !1).replace("M", "L") + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "verticalScroll": case "horizontalScroll": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 12500 * (Fs = tl); void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * Fs); var Vr, wa = 25e3 * Fs, Ia = 1e5 * Fs, Qr = 0, Xr = 0, Ft = w, Nt = P, Yr = (Vr = (Hr = Math.min(P, w)) * (Vs = xa < 0 ? 0 : wa < xa ? wa : xa) / Ia) / 2, mr = Vr / 4; "verticalScroll" == m ? (dr = Vr + Vr, vs = Nt - Yr, xs = (Ls = Nt - Vr) - Yr, pr = Ft - Yr, zn = "M" + Vr + "," + (Bs = Ft - Vr) + " L" + Vr + "," + Yr + sl(js = Vr + Yr, Yr, Yr, Yr, 180, 270, !1).replace("M", "L") + " L" + vs + "," + Qr + sl(vs, Yr, Yr, Yr, 270, 450, !1).replace("M", "L") + " L" + Ls + "," + Vr + " L" + Ls + "," + pr + sl(xs, pr, Yr, Yr, 0, 90, !1).replace("M", "L") + " L" + Yr + "," + Ft + sl(Yr, pr, Yr, Yr, 90, 270, !1).replace("M", "L") + " z M" + js + "," + Qr + sl(js, Yr, Yr, Yr, 270, 450, !1).replace("M", "L") + sl(js, js / 2, mr, mr, 90, 270, !1).replace("M", "L") + " L" + dr + "," + Yr + " M" + Ls + "," + Vr + " L" + js + "," + Vr + " M" + Vr + "," + pr + sl(Yr, pr, Yr, Yr, 0, 270, !1).replace("M", "L") + sl(Yr, (pr + Bs) / 2, mr, mr, 270, 450, !1).replace("M", "L") + " z M" + Vr + "," + pr + " L" + Vr + "," + Bs) : "horizontalScroll" == m && (pr = Vr + Vr, as = Ft - Yr, Is = (ts = Ft - Vr) - Yr, js = Nt - Vr, dr = Nt - Yr, zn = "M" + Xr + "," + (Bs = Vr + Yr) + sl(Yr, Bs, Yr, Yr, 180, 270, !1).replace("M", "L") + " L" + js + "," + Vr + " L" + js + "," + Yr + sl(dr, Yr, Yr, Yr, 180, 360, !1).replace("M", "L") + " L" + Nt + "," + Is + sl(dr, Is, Yr, Yr, 0, 90, !1).replace("M", "L") + " L" + Vr + "," + ts + " L" + Vr + "," + as + sl(Yr, as, Yr, Yr, 0, 180, !1).replace("M", "L") + " zM" + dr + "," + Vr + sl(dr, Yr, Yr, Yr, 90, -180, !1).replace("M", "L") + sl((js + dr) / 2, Yr, mr, mr, 180, 0, !1).replace("M", "L") + " z M" + dr + "," + Vr + " L" + js + "," + Vr + " M" + Yr + "," + pr + " L" + Yr + "," + Bs + sl(Bs / 2, Bs, mr, mr, 180, 360, !1).replace("M", "L") + sl(Yr, Bs, Yr, Yr, 0, 180, !1).replace("M", "L") + " M" + Vr + "," + Bs + " L" + Vr + "," + ts), g += "<path d='" + zn + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "wedgeEllipseCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = -20833 * (Fs = tl), wt = 62500 * Fs; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * Fs) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * Fs); var Wr, Zr, kt, wa = 1e5 * tl, _a = 11 * Math.PI / 180, Hr = Math.min(P, w); Kr = (z = P / 2) + ($r = P * Sa / wa), ae = (R = w / 2) + (Jr = w * wt / wa), Wr = $r * w, Zr = Jr * P, $n = (so = Math.atan(Zr / Wr)) + _a, so = so - _a, console.log("dxPos: ", $r, "dyPos: ", Jr), Ns = z * Math.cos($n), Ts = R * Math.sin($n), As = z * Math.cos(so), Os = R * Math.sin(so), zs = 0 <= $r ? (Ds = z + Ns, Rs = R + Ts, Gs = z + As, R + Os) : (Ds = z - Ns, Rs = R - Ts, Gs = z - As, R - Os), g += "<path d='" + (zn = "M" + Ds + "," + Rs + " L" + Kr + "," + ae + " L" + Gs + "," + zs + sl(z, R, z, R, 0, 360, !0)) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "wedgeRectCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = -20833 * (Fs = tl), wt = 62500 * Fs; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * Fs) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * Fs); ns = (Kr = (z = P / 2) + ($r = P * Sa / (wa = 1e5 * tl))) - z, rs = (ae = (R = w / 2) + (Jr = w * wt / wa)) - R, te = $r * w / P, Ds = P * (0 < $r ? 7 : 2) / 12, Rs = w * (0 < Jr ? 7 : 2) / 12, ee = 0 < $r ? 0 : Kr, se = 0 < Jr ? Ds : Kr, oe = 0 < $r ? Kr : P, ie = 0 < Jr ? Kr : Ds, ne = 0 < $r ? Rs : ae, le = 0 < Jr ? 0 : ae, de = 0 < $r ? ae : Rs, pe = 0 < Jr ? ae : w, g += "<path d='" + (zn = "M0,0 L" + Ds + ",0 L" + (me = 0 < (re = Math.abs(Jr) - Math.abs(te)) ? se : Ds) + "," + (be = 0 < re ? le : 0) + " L" + (Gs = P * (0 < $r ? 10 : 5) / 12) + ",0 L" + P + ",0 L" + P + "," + Rs + " L" + (ye = 0 < re ? P : oe) + "," + (ke = 0 < re ? Rs : de) + " L" + P + "," + (zs = w * (0 < Jr ? 10 : 5) / 12) + " L" + P + "," + w + " L" + Gs + "," + w + " L" + (Me = 0 < re ? ie : Ds) + "," + (xe = 0 < re ? pe : w) + " L" + Ds + "," + w + " L0," + w + " L0," + zs + " L" + (Pe = 0 < re ? 0 : ee) + "," + (we = 0 < re ? Rs : ne) + " L0," + Rs + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "wedgeRoundRectCallout": var ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = -20833 * (Fs = tl), wt = 62500 * Fs, It = 16667 * Fs; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * Fs) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * Fs) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * Fs); var $r, Jr, Kr, ae, te, re, ee, se, oe, ie, ne, le, de, pe, wa = 1e5 * tl, Hr = Math.min(P, w); Kr = (z = P / 2) + ($r = P * Sa / wa), ae = (R = w / 2) + (Jr = w * wt / wa), te = $r * w / P, Ds = P * (0 < $r ? 7 : 2) / 12, Gs = P * (0 < $r ? 10 : 5) / 12, Rs = w * (0 < Jr ? 7 : 2) / 12, zs = w * (0 < Jr ? 10 : 5) / 12, ee = 0 < $r ? 0 : Kr, Pe = 0 < (re = Math.abs(Jr) - Math.abs(te)) ? 0 : ee, se = 0 < Jr ? Ds : Kr, me = 0 < re ? se : Ds, oe = 0 < $r ? Kr : P, ye = 0 < re ? P : oe, ie = 0 < Jr ? Kr : Ds, Me = 0 < re ? ie : Ds, ne = 0 < $r ? Rs : ae, we = 0 < re ? Rs : ne, le = 0 < Jr ? 0 : ae, be = 0 < re ? le : 0, de = 0 < $r ? ae : Rs, ke = 0 < re ? Rs : de, pe = 0 < Jr ? ae : w, xe = 0 < re ? pe : w, xo = P - (Mo = Hr * It / wa), Xi = w - Mo, g += "<path d='" + (zn = "M0," + Mo + sl(Mo, Mo, Mo, Mo, 180, 270, !1).replace("M", "L") + " L" + Ds + ",0 L" + me + "," + be + " L" + Gs + ",0 L" + xo + ",0" + sl(xo, Mo, Mo, Mo, 270, 360, !1).replace("M", "L") + " L" + P + "," + Rs + " L" + ye + "," + ke + " L" + P + "," + zs + " L" + P + "," + Xi + sl(xo, Xi, Mo, Mo, 0, 90, !1).replace("M", "L") + " L" + Gs + "," + w + " L" + Me + "," + xe + " L" + Ds + "," + w + " L" + Mo + "," + w + sl(Mo, Xi, Mo, Mo, 90, 180, !1).replace("M", "L") + " L0," + zs + " L" + Pe + "," + we + " L0," + Rs + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "accentBorderCallout1": case "accentBorderCallout2": case "accentBorderCallout3": case "borderCallout1": case "borderCallout2": case "borderCallout3": case "accentCallout1": case "accentCallout2": case "accentCallout3": case "callout1": case "callout2": case "callout3": var ce, he, ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 18750 * (Fs = tl), wt = -8333 * Fs, It = 18750 * Fs, fe = -16667 * Fs, ue = 1e5 * Fs, ve = -16667 * Fs, Le = 112963 * Fs, ge = -8333 * Fs; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * Fs) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * Fs) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * Fs) : "adj4" == to ? (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * Fs) : "adj5" == to ? (ao = ul(ha[fa], ["attrs", "fmla"]), ue = parseInt(ao.substr(4)) * Fs) : "adj6" == to ? (ce = ul(ha[fa], ["attrs", "fmla"]), ve = parseInt(ce.substr(4)) * Fs) : "adj7" == to ? (he = ul(ha[fa], ["attrs", "fmla"]), Le = parseInt(he.substr(4)) * Fs) : "adj8" == to && (he = ul(ha[fa], ["attrs", "fmla"]), ge = parseInt(he.substr(4)) * Fs); wa = 1e5 * Fs; switch (m) { case "borderCallout1": case "callout1": void 0 === ha && (Sa = 18750 * Fs, wt = -8333 * Fs, It = 112500 * Fs, fe = -38333 * Fs), zn = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Ds = P * wt / wa) + "," + (Rs = w * Sa / wa) + " L" + (Gs = P * fe / wa) + "," + (zs = w * It / wa); break; case "borderCallout2": case "callout2": void 0 === ha && (wt = -8333 * Fs, It = Sa = 18750 * Fs, fe = -16667 * Fs, ue = 112500 * Fs, ve = -46667 * Fs), zn = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Ds = P * wt / wa) + "," + (Rs = w * Sa / wa) + " L" + (Gs = P * fe / wa) + "," + (zs = w * It / wa) + " L" + (js = P * ve / wa) + "," + (Bs = w * ue / wa) + " L" + Gs + "," + zs; break; case "borderCallout3": case "callout3": void 0 === ha && (It = Sa = 18750 * Fs, ue = 1e5 * Fs, ve = fe = -16667 * Fs, Le = 112963 * Fs, ge = wt = -8333 * Fs), zn = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Ds = P * wt / wa) + "," + (Rs = w * Sa / wa) + " L" + (Gs = P * fe / wa) + "," + (zs = w * It / wa) + " L" + (js = P * ve / wa) + "," + (Bs = w * ue / wa) + " L" + (dr = P * ge / wa) + "," + (pr = w * Le / wa) + " L" + js + "," + Bs + " L" + Gs + "," + zs; break; case "accentBorderCallout1": case "accentCallout1": void 0 === ha && (Sa = 18750 * Fs, wt = -8333 * Fs, It = 112500 * Fs, fe = -38333 * Fs), zn = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Ds = P * wt / wa) + "," + (Rs = w * Sa / wa) + " L" + (Gs = P * fe / wa) + "," + (zs = w * It / wa) + " M" + Ds + ",0 L" + Ds + "," + w; break; case "accentBorderCallout2": case "accentCallout2": void 0 === ha && (wt = -8333 * Fs, It = Sa = 18750 * Fs, fe = -16667 * Fs, ue = 112500 * Fs, ve = -46667 * Fs), zn = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Ds = P * wt / wa) + "," + (Rs = w * Sa / wa) + " L" + (Gs = P * fe / wa) + "," + (zs = w * It / wa) + " L" + (js = P * ve / wa) + "," + (Bs = w * ue / wa) + " L" + Gs + "," + zs + " M" + Ds + ",0 L" + Ds + "," + w; break; case "accentBorderCallout3": case "accentCallout3": void 0 === ha && (It = Sa = 18750 * Fs, ue = 1e5 * Fs, ve = fe = -16667 * Fs, Le = 112963 * Fs, ge = wt = -8333 * Fs), zn = "M0,0 L" + P + ",0 L" + P + "," + w + " L0," + w + " z M" + (Ds = P * wt / wa) + "," + (Rs = w * Sa / wa) + " L" + (Gs = P * fe / wa) + "," + (zs = w * It / wa) + " L" + (js = P * ve / wa) + "," + (Bs = w * ue / wa) + " L" + (dr = P * ge / wa) + "," + (pr = w * Le / wa) + " L" + js + "," + Bs + " L" + Gs + "," + zs + " M" + Ds + ",0 L" + Ds + "," + w }g += "<path d='" + zn + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftRightRibbon": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 5e4 * (Fs = tl), wt = 5e4 * Fs, It = 16667 * Fs; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * Fs) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * Fs) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * Fs); var be, me, ye, ke, Me, xe, Pe, we, wa = 33333 * Fs, zt = 2e5 * Fs, Ie = 4e5 * Fs; Pt = (Ia = 1e5 * Fs) - (ws = It < 0 ? 0 : wa < It ? wa : It), xt = Ia * ((z = P / 2) - (Se = P / 32)) / (Hr = Math.min(P, w)), dr = P - (Ds = Hr * (zo = wt < 0 ? 0 : xt < wt ? xt : wt) / Ia), me = (R = w / 2) + (Ts = w * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt) - (Os = w * ws / -zt), ke = w - (ye = (be = R + Os - Ts) + Ts), xe = w - (Me = 2 * ye), Gs = z - Se, js = z + Se, zs = (we = w - (Pe = Me - be)) - (Wn = ws * Hr / Ie), g += "<path d='" + (zn = "M0," + ye + "L" + Ds + ",0L" + Ds + "," + be + "L" + z + "," + be + sl(z, Rs = be + Wn, Se, Wn, 270, 450, !1).replace("M", "L") + sl(z, zs, Se, Wn, 270, 90, !1).replace("M", "L") + "L" + dr + "," + we + "L" + dr + "," + xe + "L" + P + "," + ke + "L" + dr + "," + w + "L" + dr + "," + me + "L" + z + "," + me + sl(z, me - Wn, Se, Wn, 90, 180, !1).replace("M", "L") + "L" + Gs + "," + Pe + "L" + Ds + "," + Pe + "L" + Ds + "," + Me + " zM" + js + "," + Rs + "L" + js + "," + we + "M" + Gs + "," + zs + "L" + Gs + "," + Pe) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "ribbon": case "ribbon2": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 16667 * tl, wt = 5e4 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl); Ia = 33333 * tl, zt = 75e3 * tl, Ie = 1e5 * tl, Qr = 0, Xr = 0, Ft = w; Re = (Nt = P) - (Ce = P / 8), js = (Gs = (z = P / 2) - (As = P * (zo = wt < (wa = 25e3 * tl) ? wa : zt < wt ? zt : wt) / (_s = 2e5 * tl))) + (Se = P / 32), Jt = (Kt = z + As) - Se, dr = (xs = Gs + Ce) - Se, vs = (Ls = Kt - Ce) + Se, Wn = w * (eo = Sa < 0 ? 0 : Ia < Sa ? Ia : Sa) / (Ss = 4e5 * tl), "ribbon2" == m ? (zs = Ft - (Os = w * eo / Ie), ts = Ft - Wn, as = (Rs = Ft - (Ts = w * eo / _s)) - Wn, zn = "M" + Xr + "," + Ft + " L" + Ce + "," + (Bs = ((pr = Qr + Os) + Ft) / 2) + " L" + Xr + "," + pr + " L" + Gs + "," + pr + " L" + Gs + "," + Wn + sl(js, Wn, Se, Wn, 180, 270, !1).replace("M", "L") + " L" + Jt + "," + Qr + sl(Jt, Wn, Se, Wn, 270, 360, !1).replace("M", "L") + " L" + Kt + "," + pr + " L" + Kt + "," + pr + " L" + Nt + "," + pr + " L" + Re + "," + Bs + " L" + Nt + "," + Ft + " L" + vs + "," + Ft + sl(vs, ts, Se, Wn, 90, 270, !1).replace("M", "L") + " L" + Jt + "," + Rs + sl(Jt, as, Se, Wn, 90, -90, !1).replace("M", "L") + " L" + js + "," + zs + sl(js, as, Se, Wn, 270, 90, !1).replace("M", "L") + " L" + dr + "," + Rs + sl(dr, ts, Se, Wn, 270, 450, !1).replace("M", "L") + " z M" + xs + "," + zs + " L" + xs + "," + ts + "M" + Ls + "," + ts + " L" + Ls + "," + zs + "M" + Gs + "," + as + " L" + Gs + "," + pr + "M" + Kt + "," + pr + " L" + Kt + "," + as) : "ribbon" == m && (Rs = w * eo / _s, Bs = (pr = Ft - (zs = w * eo / Ie)) / 2, Is = Ft - Wn, ts = zs - Wn, zn = "M" + Xr + "," + Qr + " L" + dr + "," + Qr + sl(dr, Wn, Se, Wn, 270, 450, !1).replace("M", "L") + " L" + js + "," + Rs + sl(js, ts, Se, Wn, 270, 90, !1).replace("M", "L") + " L" + Jt + "," + zs + sl(Jt, ts, Se, Wn, 90, -90, !1).replace("M", "L") + " L" + vs + "," + Rs + sl(vs, Wn, Se, Wn, 90, 270, !1).replace("M", "L") + " L" + Nt + "," + Qr + " L" + Re + "," + Bs + " L" + Nt + "," + pr + " L" + Kt + "," + pr + " L" + Kt + "," + Is + sl(Jt, Is, Se, Wn, 0, 90, !1).replace("M", "L") + " L" + js + "," + Ft + sl(js, Is, Se, Wn, 90, 180, !1).replace("M", "L") + " L" + Gs + "," + pr + " L" + Xr + "," + pr + " L" + Ce + "," + Bs + " z M" + xs + "," + Wn + " L" + xs + "," + zs + "M" + Ls + "," + zs + " L" + Ls + "," + Wn + "M" + Gs + "," + pr + " L" + Gs + "," + ts + "M" + Kt + "," + ts + " L" + Kt + "," + pr), g += "<path d='" + zn + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "doubleWave": case "wave": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = "doubleWave" == m ? 6250 * tl : 12500 * tl, wt = 0; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl); var _e, Ia = -1e4 * tl, zt = 5e4 * tl, Ie = 1e5 * tl, z = P / 2, Qr = 0, Xr = 0, Ft = w, Nt = P, Ce = P / 8, Se = P / 32; "doubleWave" == m ? (wa = 12500 * tl, zn = "M" + (Gs = Xr - (As = 0 < (Be = P * (zo = wt < Ia ? Ia : Ie < wt ? Ie : wt) / zt) ? 0 : Be)) + "," + (Rs = w * (eo = Sa < 0 ? 0 : wa < Sa ? wa : Sa) / Ie) + " C" + (js = Gs + ($e = (As + (Jt = Nt - (Se = 0 < Be ? Be : 0))) / 6)) + "," + (zs = Rs - (Os = 10 * Rs / 3)) + " " + (dr = Gs + (Oe = (As + Jt) / 3)) + "," + (Bs = Rs + Os) + " " + (xs = (Gs + Jt) / 2) + "," + Rs + " C" + (Ls = xs + $e) + "," + zs + " " + (vs = (Ls + Jt) / 2) + "," + Bs + " " + Jt + "," + Rs + " L" + (_e = Nt + As) + "," + (pr = Ft - Rs) + " C" + (Ae = ((De = (Ge = ((Kt = Xr + Se) + _e) / 2) + $e) + _e) / 2) + "," + (ts = pr + Os) + " " + De + "," + (Is = pr - Os) + " " + Ge + "," + pr + " C" + (Te = Kt + Oe) + "," + ts + " " + (Re = Kt + $e) + "," + Is + " " + Kt + "," + pr + " z") : "wave" == m && (_s = 2e4 * tl, zn = "M" + (Gs = Xr - (As = 0 < (Be = P * (zo = wt < Ia ? Ia : Ie < wt ? Ie : wt) / zt) ? 0 : Be)) + "," + (Rs = w * (eo = Sa < 0 ? 0 : _s < Sa ? _s : Sa) / Ie) + " C" + (js = Gs + ($e = (As + (xs = Nt - (Ve = 0 < Be ? Be : 0))) / 3)) + "," + (zs = Rs - (Os = 10 * Rs / 3)) + " " + (dr = (js + xs) / 2) + "," + (Bs = Rs + Os) + " " + xs + "," + Rs + " L" + (Re = Nt + As) + "," + (pr = Ft - Rs) + " C" + (Jt = ((vs = (Ls = Xr + Ve) + $e) + Re) / 2) + "," + (ts = pr + Os) + " " + vs + "," + (Is = pr - Os) + " " + Ls + "," + pr + " z"), g += "<path d='" + zn + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "ellipseRibbon": case "ellipseRibbon2": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 5e4 * tl, It = 12500 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); var Te, Ae, De, Ge, je, Re, ze, Be, Fe, zt = 75e3 * tl, Ie = 1e5 * tl, Qr = 0, Xr = 0; dr = (Nt = P) - (js = (Gs = (z = P / 2) - (As = P * (zo = wt < (wa = 25e3 * tl) ? wa : zt < wt ? zt : wt) / (_s = 2e5 * tl))) + (Ce = P / 8)), xs = Nt - Gs, Ls = Nt - Ce, hi = js - (ci = js * js / P), De = Nt - (Ae = js / 2), Es = (ci = w * (eo = Sa < 0 ? 0 : Ie < Sa ? Ie : Sa) / Ie) - (Ts = w * (ws = It < (Ge = (Pi = eo - (Mi = (ki = Ie - eo) / 2)) < 0 ? 0 : Pi) ? Ge : eo < It ? eo : It) / Ie), vi = (Te = 4 * Ts / P) * (ui = Gs - Gs * Gs / P), Be = (Ft = w) - ci, gi = 14 * Ts / 16, yi = Te * (Re = Gs / 2), Ge = Nt - Re, "ellipseRibbon" == m ? (as = (Rs = Te * hi) + Es, er = Ft - Ts, zn = "M" + Xr + "," + Qr + " Q" + Ae + "," + (je = Te * Ae) + " " + js + "," + Rs + " L" + Gs + "," + (Bs = vi + Es) + " Q" + z + "," + (ze = (Li = Ts + Es - Bs + Ts) + Es) + " " + xs + "," + Bs + " L" + dr + "," + Rs + " Q" + De + "," + je + " " + Nt + "," + Qr + " L" + Ls + "," + (zs = (gi + Be) / 2) + " L" + Nt + "," + Be + " Q" + Ge + "," + (Fe = yi + Be) + " " + xs + "," + (Is = vi + Be) + " L" + xs + "," + (ts = Bs + Be) + " Q" + z + "," + (ze + Be) + " " + Gs + "," + ts + " L" + Gs + "," + Is + " Q" + Re + "," + Fe + " " + Xr + "," + Be + " L" + Ce + "," + zs + " zM" + Gs + "," + Is + " L" + Gs + "," + Bs + "M" + xs + "," + Bs + " L" + xs + "," + Is + "M" + js + "," + Rs + " L" + js + "," + as + "M" + dr + "," + as + " L" + dr + "," + Rs) : "ellipseRibbon2" == m && (Io = (Mo = Te * hi) + Es, zn = "M" + Xr + "," + Ft + " L" + Ce + "," + (zs = Ft - (xo = (gi + Be) / 2)) + " L" + Xr + "," + ci + " Q" + Re + "," + (Fe = Ft - (yi + Be)) + " " + Gs + "," + (Is = Ft - (vi + Be)) + " L" + Gs + "," + (ts = Ft - ((Po = vi + Es) + Be)) + " Q" + z + "," + (Ft - ((Re = (Li = Ts + Es - Po + Ts) + Es) + Be)) + " " + xs + "," + ts + " L" + xs + "," + Is + " Q" + Ge + "," + Fe + " " + Nt + "," + ci + " L" + Ls + "," + zs + " L" + Nt + "," + Ft + " Q" + De + "," + (je = Ft - Te * Ae) + " " + dr + "," + (Rs = Ft - Mo) + " L" + xs + "," + (Bs = Ft - Po) + " Q" + z + "," + (ze = Ft - Re) + " " + Gs + "," + Bs + " L" + js + "," + Rs + " Q" + Ae + "," + je + " " + Xr + "," + Ft + " zM" + Gs + "," + Bs + " L" + Gs + "," + Is + "M" + xs + "," + Is + " L" + xs + "," + Bs + "M" + js + "," + (as = Ft - Io) + " L" + js + "," + Rs + "M" + dr + "," + Rs + " L" + dr + "," + as), g += "<path d='" + zn + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "line": case "straightConnector1": case "bentConnector4": case "bentConnector5": case "curvedConnector2": case "curvedConnector3": case "curvedConnector4": case "curvedConnector5": g += "<line x1='0' y1='0' x2='" + P + "' y2='" + w + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' ", void 0 === T || "triangle" !== T.type && "arrow" !== T.type || (g += "marker-start='url(#markerTriangle_" + b + ")' "), void 0 === A || "triangle" !== A.type && "arrow" !== A.type || (g += "marker-end='url(#markerTriangle_" + b + ")' "), g += "/>"; break; case "rightArrow": var va = .25, La = .5, Ne = P / w; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = .5 - parseInt(Zs.substr(4)) / 2e5) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = 1 - parseInt($s.substr(4)) / 1e5 / Ne); g += " <polygon points='" + P + " " + w / 2 + "," + La * P + " 0," + La * P + " " + va * w + ",0 " + va * w + ",0 " + (1 - va) * w + "," + La * P + " " + (1 - va) * w + ", " + La * P + " " + w + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftArrow": va = .25, La = .5, Ne = P / w; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = .5 - parseInt(Zs.substr(4)) / 2e5) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 1e5 / Ne); g += " <polygon points='0 " + w / 2 + "," + La * P + " " + w + "," + La * P + " " + (1 - va) * w + "," + P + " " + (1 - va) * w + "," + P + " " + va * w + "," + La * P + " " + va * w + ", " + La * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "downArrow": case "flowChartOffpageConnector": va = .25, La = .5, Ne = w / P; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = parseInt(Zs.substr(4)) / 2e5) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 1e5 / Ne); "flowChartOffpageConnector" == m && (va = .5, La = .212), g += " <polygon points='" + (.5 - va) * P + " 0," + (.5 - va) * P + " " + (1 - La) * w + ",0 " + (1 - La) * w + "," + P / 2 + " " + w + "," + P + " " + (1 - La) * w + "," + (.5 + va) * P + " " + (1 - La) * w + ", " + (.5 + va) * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "upArrow": va = .25, La = .5, Ne = w / P; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = parseInt(Zs.substr(4)) / 2e5) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 1e5 / Ne); g += " <polygon points='" + P / 2 + " 0,0 " + La * w + "," + (.5 - va) * P + " " + La * w + "," + (.5 - va) * P + " " + w + "," + (.5 + va) * P + " " + w + "," + (.5 + va) * P + " " + La * w + ", " + P + " " + La * w + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftRightArrow": va = .25, La = .25, Ne = P / w; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = .5 - parseInt(Zs.substr(4)) / 2e5) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 1e5 / Ne); g += " <polygon points='0 " + w / 2 + "," + La * P + " " + w + "," + La * P + " " + (1 - va) * w + "," + (1 - La) * P + " " + (1 - va) * w + "," + (1 - La) * P + " " + w + "," + P + " " + w / 2 + ", " + (1 - La) * P + " 0," + (1 - La) * P + " " + va * w + "," + La * P + " " + va * w + "," + La * P + " 0' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "upDownArrow": va = .25, La = .25, Ne = w / P; if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), va = .5 - parseInt(Zs.substr(4)) / 2e5) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), La = parseInt($s.substr(4)) / 1e5 / Ne); g += " <polygon points='" + P / 2 + " 0,0 " + La * w + "," + va * P + " " + La * w + "," + va * P + " " + (1 - La) * w + ",0 " + (1 - La) * w + "," + P / 2 + " " + w + ", " + P + " " + (1 - La) * w + "," + (1 - va) * P + " " + (1 - La) * w + "," + (1 - va) * P + " " + La * w + "," + P + " " + La * w + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "quadArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 22500 * tl, wt = 22500 * tl, It = 22500 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); z = P / 2; Ps = (ci = Ia - (Pt = 2 * (zo = wt < 0 ? 0 : wa < wt ? wa : wt))) / 2, g += "<path d='" + (zn = "M0," + (R = w / 2) + " L" + (Ds = (Ye = Math.min(P, w)) * (ws = It < 0 ? 0 : Ps < It ? Ps : It) / Ia) + "," + (zs = R - (As = Ye * zo / Ia)) + " L" + Ds + "," + (Bs = R - ($e = Ye * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + " L" + (js = z - $e) + "," + Bs + " L" + js + "," + Ds + " L" + (Gs = z - As) + "," + Ds + " L" + z + ",0 L" + (xs = z + As) + "," + Ds + " L" + (dr = z + $e) + "," + Ds + " L" + dr + "," + Bs + " L" + (Ls = P - Ds) + "," + Bs + " L" + Ls + "," + zs + " L" + P + "," + R + " L" + Ls + "," + (Is = R + As) + " L" + Ls + "," + (pr = R + $e) + " L" + dr + "," + pr + " L" + dr + "," + (ts = w - Ds) + " L" + xs + "," + ts + " L" + z + "," + w + " L" + Gs + "," + ts + " L" + js + "," + ts + " L" + js + "," + pr + " L" + Ds + "," + pr + " L" + Ds + "," + Is + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftRightUpArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); R = w / 2, z = P / 2; Ps = (ci = Ia - (Pt = 2 * (zo = wt < 0 ? 0 : wa < wt ? wa : wt))) / 2, g += "<path d='" + (zn = "M0," + (pr = w - (As = (Ye = Math.min(P, w)) * zo / Ia)) + " L" + (Ds = Ye * (ws = It < 0 ? 0 : Ps < It ? Ps : It) / Ia) + "," + (zs = w - (Os = Ye * zo / wa)) + " L" + Ds + "," + (Bs = pr - ($e = Ye * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + " L" + (js = z - $e) + "," + Bs + " L" + js + "," + Ds + " L" + (Gs = z - As) + "," + Ds + " L" + z + ",0 L" + (xs = z + As) + "," + Ds + " L" + (dr = z + $e) + "," + Ds + " L" + dr + "," + Bs + " L" + (Ls = P - Ds) + "," + Bs + " L" + Ls + "," + zs + " L" + P + "," + pr + " L" + Ls + "," + w + " L" + Ls + "," + (Is = pr + $e) + " L" + Ds + "," + Is + " L" + Ds + "," + w + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftUpArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); var Oe, R = w / 2, z = P / 2; Ps = Ia - (Pt = 2 * (zo = wt < 0 ? 0 : wa < wt ? wa : wt)), g += "<path d='" + (zn = "M0," + (pr = w - (Oe = (Ye = Math.min(P, w)) * zo / Ia)) + " L" + (Ds = Ye * (ws = It < 0 ? 0 : Ps < It ? Ps : It) / Ia) + "," + (zs = w - (As = Ye * zo / wa)) + " L" + Ds + "," + (Bs = pr - ($e = Ye * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + " L" + (js = (dr = P - Oe) - $e) + "," + Bs + " L" + js + "," + Ds + " L" + (Gs = P - As) + "," + Ds + " L" + dr + ",0 L" + P + "," + Ds + " L" + (xs = dr + $e) + "," + Ds + " L" + xs + "," + (Is = pr + $e) + " L" + Ds + "," + Is + " L" + Ds + "," + w + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "bentUpArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); R = w / 2, z = P / 2; eo = Sa < 0 ? 0 : wa < Sa ? wa : Sa, g += "<path d='" + (zn = "M0," + (zs = w - (Os = (Ye = Math.min(P, w)) * eo / Ia)) + " L" + (Gs = (js = P - ($e = Ye * (zo = wt < 0 ? 0 : wa < wt ? wa : wt) / Ia)) - (As = Ye * eo / zt)) + "," + zs + " L" + Gs + "," + (Rs = Ye * (ws = It < 0 ? 0 : Ps < It ? Ps : It) / Ia) + " L" + (Ds = P - (Ns = Ye * zo / wa)) + "," + Rs + " L" + js + ",0 L" + P + "," + Rs + " L" + (dr = js + As) + "," + Rs + " L" + dr + "," + w + " L0," + w + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "bentArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, fe = 43750 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to && (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl); Pt = 2 * (zo = wt < 0 ? 0 : wa < wt ? wa : wt), We = Ia * ((He = P - (is = (Ye = Math.min(P, w)) * (ws = It < 0 ? 0 : wa < It ? wa : It) / Ia)) < (Qe = w - (Ee = (ds = Ye * zo / Ia) - (no = (oo = Ye * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / Ia) / 2))) ? He : Qe) / Ye, js = oo + (Qe = 0 < (Ue = (qe = Ye * (Ze = fe < 0 ? 0 : We < fe ? We : fe) / Ia) - oo) ? Ue : 0), dr = P - is, pr = (Bs = Ee + oo) + Ee, ts = Bs + Qe, g += "<path d='" + (zn = "M0," + w + " L0," + (Is = Ee + qe) + sl(qe, Is, qe, qe, 180, 270, !1).replace("M", "L") + " L" + dr + "," + Ee + " L" + dr + ",0 L" + P + "," + ds + " L" + dr + "," + pr + " L" + dr + "," + Bs + " L" + js + "," + Bs + sl(js, ts, Qe, Qe, 270, 180, !1).replace("M", "L") + " L" + oo + "," + w + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "uturnArrow": var Ee, He, qe, Ue, ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, fe = 43750 * tl, ue = 75e3 * tl, wa = 25e3 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to ? (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl) : "adj5" == to && (ao = ul(ha[fa], ["attrs", "fmla"]), ue = parseInt(ao.substr(4)) * tl); Pt = 2 * (zo = wt < 0 ? 0 : wa < wt ? wa : wt), Ps = (Ia - (hi = (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) * (Ye = Math.min(P, w)) / w)) * w / Ye, We = Ia * ((He = (Kt = P - (Ee = (ds = Ye * zo / Ia) - (no = (oo = Ye * eo / Ia) / 2))) / 2) < (pr = (Is = w * (ro = ue < (ro = (ci = (ws = It < 0 ? 0 : Ps < It ? Ps : It) + eo) * Ye / w) ? ro : Ia < ue ? Ia : ue) / Ia) - (is = Ye * ws / Ia)) ? He : pr) / Ye, js = oo + (Qe = 0 < (Ue = (qe = Ye * (Ze = fe < 0 ? 0 : We < fe ? We : fe) / Ia) - oo) ? Ue : 0), dr = Kt - qe, xs = (vs = (Ls = (Jt = P - ds) - ds) + Ee) - Qe, cx = (oo + vs) / 2; g += "<path d='" + (zn = "M0," + w + " L0," + qe + sl(qe, qe, qe, qe, 180, 270, !1).replace("M", "L") + " L" + dr + ",0" + sl(dr, qe, qe, qe, 270, 360, !1).replace("M", "L") + " L" + Kt + "," + pr + " L" + P + "," + pr + " L" + Jt + "," + Is + " L" + Ls + "," + pr + " L" + vs + "," + pr + " L" + vs + "," + js + sl(xs, js, Qe, Qe, 0, -90, !1).replace("M", "L") + " L" + js + "," + oo + sl(js, js, Qe, Qe, 270, 180, !1).replace("M", "L") + " L" + oo + "," + w + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "stripedRightArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 5e4 * tl, wt = 5e4 * tl, wa = 1e5 * tl, Ia = 2e5 * tl, zt = 84375 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl); var Ve, br, R = w / 2; xt = zt * P / (Ye = Math.min(P, w)); var Qe = Ye / 16, Xe = Ye / 32; g += "<path d='" + (zn = "M0," + (Rs = R - (Ts = w * (eo = Sa < 0 ? 0 : wa < Sa ? wa : Sa) / Ia)) + " L" + Xe + "," + Rs + " L" + Xe + "," + (zs = R + Ts) + " L0," + zs + " z M" + Qe + "," + Rs + " L" + (Xe = Ye / 8) + "," + Rs + " L" + Xe + "," + zs + " L" + Qe + "," + zs + " z M" + (dr = 5 * Ye / 32) + "," + Rs + " L" + (xs = P - (Ve = Ye * (zo = wt < 0 ? 0 : xt < wt ? xt : wt) / wa)) + "," + Rs + " L" + xs + ",0 L" + P + "," + R + " L" + xs + "," + w + " L" + xs + "," + zs + " L" + dr + "," + zs + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "notchedRightArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 5e4 * tl, wt = 5e4 * tl, wa = 1e5 * tl, Ia = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl); Ca = R = w / 2; xt = wa * P / (Ye = Math.min(P, w)), g += "<path d='" + (zn = "M0," + (Rs = R - (Ts = w * (eo = Sa < 0 ? 0 : wa < Sa ? wa : Sa) / Ia)) + " L" + (Gs = P - (As = Ye * (zo = wt < 0 ? 0 : xt < wt ? xt : wt) / wa)) + "," + Rs + " L" + Gs + ",0 L" + P + "," + R + " L" + Gs + "," + w + " L" + Gs + "," + (zs = R + Ts) + " L0," + zs + " L" + (Ds = Ts * As / Ca) + "," + R + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "homePlate": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 5e4 * tl, wa = 1e5 * tl; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); R = w / 2; Aa = wa * P / (Ye = Math.min(P, w)), g += "<path  d='" + (zn = "M0,0 L" + (Ds = P - (Ns = Ye * (Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa) / wa)) + ",0 L" + P + "," + R + " L" + Ds + "," + w + " L0," + w + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "chevron": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 5e4 * tl, wa = 1e5 * tl; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); var Ye, R = w / 2; Aa = wa * P / (Ye = Math.min(P, w)), g += "<path d='" + (zn = "M0,0 L" + (Gs = P - (Ds = Ye * (Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa) / wa)) + ",0 L" + P + "," + R + " L" + Gs + "," + w + " L0," + w + " L" + Ds + "," + R + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "rightArrowCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, fe = 64977 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to && (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl); R = w / 2, Nt = P, Ft = w, Xr = 0, Qr = 0; xt = wa * w / (Hr = Math.min(P, w)), Pt = 2 * (zo = wt < 0 ? 0 : xt < wt ? xt : wt), Ps = Ia * P / Hr, We = ka - (hi = (ws = It < 0 ? 0 : Ps < It ? Ps : It) * Hr / P), Ds = (Gs = P * (Ze = fe < 0 ? 0 : We < fe ? We : fe) / Ia) / 2, g += "<path d='" + (zn = "M" + Xr + "," + Qr + " L" + Gs + "," + Qr + " L" + Gs + "," + (zs = R - (Os = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + " L" + (js = Nt - ($e = Hr * ws / Ia)) + "," + zs + " L" + js + "," + (Rs = R - (Ts = Hr * zo / Ia)) + " L" + Nt + "," + R + " L" + js + "," + (pr = R + Ts) + " L" + js + "," + (Bs = R + Os) + " L" + Gs + "," + Bs + " L" + Gs + "," + Ft + " L" + Xr + "," + Ft + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "downArrowCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, fe = 64977 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to && (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl); z = P / 2, Nt = P, Ft = w, Xr = 0, Qr = 0; xt = wa * P / (Hr = Math.min(P, w)), Pt = 2 * (zo = wt < 0 ? 0 : xt < wt ? xt : wt), Ps = Ia * w / Hr, We = Ia - (hi = (ws = It < 0 ? 0 : Ps < It ? Ps : It) * Hr / w), Rs = (zs = w * (Ze = fe < 0 ? 0 : We < fe ? We : fe) / Ia) / 2, g += "<path d='" + (zn = "M" + Xr + "," + Qr + " L" + Nt + "," + Qr + " L" + Nt + "," + zs + " L" + (js = z + (As = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + "," + zs + " L" + js + "," + (Bs = Ft - (Es = Hr * ws / Ia)) + " L" + (dr = z + (Ns = Hr * zo / Ia)) + "," + Bs + " L" + z + "," + Ft + " L" + (Ds = z - Ns) + "," + Bs + " L" + (Gs = z - As) + "," + Bs + " L" + Gs + "," + zs + " L" + Xr + "," + zs + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftArrowCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, fe = 64977 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to && (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl); R = w / 2, Nt = P, Ft = w, Xr = 0, Qr = 0; xt = wa * w / (Hr = Math.min(P, w)), Pt = 2 * (zo = wt < 0 ? 0 : xt < wt ? xt : wt), Ps = Ia * P / Hr, We = Ia - (hi = (ws = It < 0 ? 0 : Ps < It ? Ps : It) * Hr / P), js = ((Gs = Nt - (As = P * (Ze = fe < 0 ? 0 : We < fe ? We : fe) / Ia)) + Nt) / 2, g += "<path d='" + (zn = "M" + Xr + "," + R + " L" + (Ds = Hr * ws / Ia) + "," + (Rs = R - (Ts = Hr * zo / Ia)) + " L" + Ds + "," + (zs = R - (Os = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + " L" + Gs + "," + zs + " L" + Gs + "," + Qr + " L" + Nt + "," + Qr + " L" + Nt + "," + Ft + " L" + Gs + "," + Ft + " L" + Gs + "," + (Bs = R + Os) + " L" + Ds + "," + Bs + " L" + Ds + "," + (pr = R + Ts) + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "upArrowCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, fe = 64977 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to && (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl); z = P / 2, Nt = P, Ft = w, Xr = 0, Qr = 0; xt = wa * P / (Hr = Math.min(P, w)), Pt = 2 * (zo = wt < 0 ? 0 : xt < wt ? xt : wt), Ps = Ia * w / Hr, We = Ia - (hi = (ws = It < 0 ? 0 : Ps < It ? Ps : It) * Hr / w), Bs = ((zs = Ft - (Os = w * (Ze = fe < 0 ? 0 : We < fe ? We : fe) / Ia)) + Ft) / 2, g += "<path d='" + (zn = "M" + Xr + "," + zs + " L" + (Gs = z - (As = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + "," + zs + " L" + Gs + "," + (Rs = Hr * ws / Ia) + " L" + (Ds = z - (Ns = Hr * zo / Ia)) + "," + Rs + " L" + z + "," + Qr + " L" + (dr = z + Ns) + "," + Rs + " L" + (js = z + As) + "," + Rs + " L" + js + "," + zs + " L" + Nt + "," + zs + " L" + Nt + "," + Ft + " L" + Xr + "," + Ft + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftRightArrowCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 25e3 * tl, It = 25e3 * tl, fe = 48123 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to && (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl); R = w / 2, z = P / 2, Nt = P, Ft = w, Xr = 0, Qr = 0; xt = wa * w / (Hr = Math.min(P, w)), Pt = 2 * (zo = wt < 0 ? 0 : xt < wt ? xt : wt), Ps = wa * P / Hr, We = Ia - (hi = (ws = It < 0 ? 0 : Ps < It ? Ps : It) * Hr / oa), g += "<path d='" + (zn = "M" + Xr + "," + R + " L" + (Ds = Hr * ws / Ia) + "," + (Rs = R - (Ts = Hr * zo / Ia)) + " L" + Ds + "," + (zs = R - (Os = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + " L" + (Gs = z - (As = P * (Ze = fe < 0 ? 0 : We < fe ? We : fe) / zt)) + "," + zs + " L" + Gs + "," + Qr + " L" + (js = z + As) + "," + Qr + " L" + js + "," + zs + " L" + (dr = Nt - Ds) + "," + zs + " L" + dr + "," + Rs + " L" + Nt + "," + R + " L" + dr + "," + (pr = R + Ts) + " L" + dr + "," + (Bs = R + Os) + " L" + js + "," + Bs + " L" + js + "," + Ft + " L" + Gs + "," + Ft + " L" + Gs + "," + Bs + " L" + Ds + "," + Bs + " L" + Ds + "," + pr + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "quadArrowCallout": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 18515 * tl, wt = 18515 * tl, It = 18515 * tl, fe = 48123 * tl, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl) : "adj4" == to && (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) * tl); var We, Ze, $e, z = P / 2, Nt = P, Ft = w, Qr = 0; Pt = 2 * (zo = wt < 0 ? 0 : wa < wt ? wa : wt), Ps = wa - zo, We = Ia - (hi = 2 * (ws = It < 0 ? 0 : Ps < It ? Ps : It)), g += "<path d='" + (zn = "M" + (Xr = 0) + "," + (R = w / 2) + " L" + (is = (Hr = Math.min(P, w)) * ws / Ia) + "," + (Bs = R - (As = Hr * zo / Ia)) + " L" + is + "," + (pr = R - ($e = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / zt)) + " L" + (Gs = z - (Ns = P * (Ze = fe < eo ? eo : We < fe ? We : fe) / zt)) + "," + pr + " L" + Gs + "," + (zs = R - (Ts = w * Ze / zt)) + " L" + (dr = z - $e) + "," + zs + " L" + dr + "," + is + " L" + (js = z - As) + "," + is + " L" + z + "," + Qr + " L" + (Ls = z + As) + "," + is + " L" + (xs = z + $e) + "," + is + " L" + xs + "," + zs + " L" + (vs = z + Ns) + "," + zs + " L" + vs + "," + pr + " L" + (Jt = Nt - is) + "," + pr + " L" + Jt + "," + Bs + " L" + Nt + "," + R + " L" + Jt + "," + (ts = R + As) + " L" + Jt + "," + (Is = R + $e) + " L" + vs + "," + Is + " L" + vs + "," + (as = R + Ts) + " L" + xs + "," + as + " L" + xs + "," + (er = Ft - is) + " L" + Ls + "," + er + " L" + z + "," + Ft + " L" + js + "," + er + " L" + dr + "," + er + " L" + dr + "," + as + " L" + Gs + "," + as + " L" + Gs + "," + Is + " L" + is + "," + Is + " L" + is + "," + ts + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "curvedDownArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 5e4 * tl, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); R = w / 2, z = P / 2, oa = P / 2, Nt = P, Ft = w, Xr = 0, Qr = 0, na = 270, ia = 180, sa = 90; xt = wa * P / (Hr = Math.min(P, w)), ki = (gi = (Li = 2 * (Zn = oa - (ci = ((oo = Hr * (eo = Sa < 0 ? 0 : Ia < Sa ? Ia : Sa) / Ia) + (ss = Hr * (zo = wt < 0 ? 0 : xt < wt ? xt : wt) / Ia)) / 4))) * Li) - (yi = oo * oo), Ps = Ia * (os = (Mi = Math.sqrt(ki)) * w / Li) / Hr, ws = It < 0 ? 0 : Ps < It ? Ps : It, js = Zn + oo, ui = (hi = w * w) - (is = Hr * It / Ia) * is, dr = (xs = Zn + (ns = (vi = Math.sqrt(ui)) * Zn / w)) - (ls = (ss - oo) / 2), Jt = (vs = js + ns) + ls, Ls = Nt - (ds = ss / 2), Rs = Ft - is; var Je = 180 * (Jn = Math.atan(ns / is)) / Math.PI; ps = -Je, Pi = oo / 2; var Ke = 180 * (cs = Math.atan(Pi / os)) / Math.PI; hs = na - Ke, fs = Ke - sa, Ke = sa + Ke, g += "<path d='" + (zn = "M" + Ls + "," + Ft + " L" + dr + "," + Rs + " L" + xs + "," + Rs + sl(Zn, w, Zn, w, $n = na + Je, $n + ps, !1).replace("M", "L") + " L" + js + "," + Qr + sl(js, w, Zn, w, na, na + Je, !1).replace("M", "L") + " L" + (xs + oo) + "," + Rs + " L" + Jt + "," + Rs + " zM" + js + "," + Qr + sl(js, w, Zn, w, hs, hs + fs, !1).replace("M", "L") + sl(Zn, w, Zn, w, ia, ia + Ke, !1).replace("M", "L")) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "curvedLeftArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 5e4 * tl, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); R = w / 2, z = P / 2, Ca = w / 2, Nt = P, Ft = w, Xr = 0, Qr = 0, na = 270, ia = 180, sa = 90; xt = wa * w / (Hr = Math.min(P, w)), zo = wt < 0 ? 0 : xt < wt ? xt : wt, ki = (gi = (Li = 2 * (Wn = Ca - (ci = ((oo = Hr * (eo = Sa < 0 ? 0 : zo < Sa ? zo : Sa) / Ia) + (ss = Hr * zo / Ia)) / 4))) * Li) - (yi = oo * oo), Ps = Ia * (es = (Mi = Math.sqrt(ki)) * P / Li) / Hr, Bs = Wn + oo, ui = (hi = P * P) - (is = Hr * (ws = It < 0 ? 0 : Ps < It ? Ps : It) / Ia) * is, pr = (Is = Wn + (rs = (vi = Math.sqrt(ui)) * Wn / P)) - (ls = (ss - oo) / 2), er = (as = Bs + rs) + ls, ts = Ft - (ds = ss / 2), Ds = Xr + is, ps = -(Jn = Math.atan(rs / is)), Pi = oo / 2, fs = (cs = Math.atan(Pi / es)) - Jn, Ke = Jn + cs, ms = -cs, bs = 180 * Jn / Math.PI, Je = 180 * fs / Math.PI, Math.PI, gs = 180 * ms / Math.PI, g += "<path d='" + (zn = "M" + Nt + "," + Bs + sl(Xr, Wn, P, Wn, 0, -sa, !1).replace("M", "L") + " L" + Xr + "," + Qr + sl(Xr, Bs, P, Wn, na, na + sa, !1).replace("M", "L") + " L" + Nt + "," + Bs + sl(Xr, Bs, P, Wn, 0, bs, !1).replace("M", "L") + " L" + Ds + "," + as + " L" + Ds + "," + er + " L" + Xr + "," + ts + " L" + Ds + "," + pr + " L" + Ds + "," + Is + sl(Xr, Wn, P, Wn, bs, bs + Je, !1).replace("M", "L") + sl(Xr, Wn, P, Wn, 0, -sa, !1).replace("M", "L") + sl(Xr, Bs, P, Wn, na, na + sa, !1).replace("M", "L")) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "curvedRightArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 5e4 * tl, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); var as, ts, rs, es, R = w / 2, z = P / 2, Ca = w / 2, Nt = P, Ft = w, Xr = 0, Qr = 0, na = 270, ia = 180, sa = 90; xt = wa * w / (Hr = Math.min(P, w)), zo = wt < 0 ? 0 : xt < wt ? xt : wt, ki = (gi = (Li = 2 * (Wn = Ca - (ci = ((oo = Hr * (eo = Sa < 0 ? 0 : zo < Sa ? zo : Sa) / Ia) + (ss = Hr * zo / Ia)) / 4))) * Li) - (yi = oo * oo), Ps = Ia * (es = (Mi = Math.sqrt(ki)) * P / Li) / Hr, Bs = Wn + oo, ui = (hi = P * P) - (is = Hr * (ws = It < 0 ? 0 : Ps < It ? Ps : It) / Ia) * is, pr = (Is = Wn + (rs = (vi = Math.sqrt(ui)) * Wn / P)) - (ls = (ss - oo) / 2), er = (as = Bs + rs) + ls, ts = Ft - (ds = ss / 2), Ds = Nt - is, Jn = Math.atan(rs / is), $n = Math.PI + 0 - Jn, ps = -Jn, Pi = oo / 2, fs = (cs = Math.atan(Pi / es)) - Math.PI / 2, Ke = Math.PI / 2 + cs, ms = Math.PI - cs, rs = 180 * $n / Math.PI, es = 180 * ps / Math.PI, bs = 180 * Jn / Math.PI, ys = 180 * fs / Math.PI, g += "<path d='" + (zn = "M" + Xr + "," + Wn + sl(P, Wn, P, Wn, ia, ia + es, !1).replace("M", "L") + " L" + Ds + "," + Is + " L" + Ds + "," + pr + " L" + Nt + "," + ts + " L" + Ds + "," + er + " L" + Ds + "," + as + sl(P, Bs, P, Wn, rs, rs + bs, !1).replace("M", "L") + " L" + Xr + "," + Wn + sl(P, Wn, P, Wn, ia, ia + sa, !1).replace("M", "L") + " L" + Nt + "," + oo + sl(P, Bs, P, Wn, na, na + ys, !1).replace("M", "L")) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "curvedUpArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * tl, wt = 5e4 * tl, It = 25e3 * tl, wa = 5e4 * tl, Ia = 1e5 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * tl) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) * tl); var ss, os, is, ns, ls, ds, ps, cs, hs, R = w / 2, z = P / 2, oa = P / 2, Nt = P, Ft = w, Xr = 0, Qr = 0, na = 270, ia = 180, sa = 90; xt = wa * P / (Hr = Math.min(P, w)), ki = (gi = (Li = 2 * (Zn = oa - (ci = ((oo = Hr * (eo = Sa < 0 ? 0 : Ia < Sa ? Ia : Sa) / Ia) + (ss = Hr * (zo = wt < 0 ? 0 : xt < wt ? xt : wt) / Ia)) / 4))) * Li) - (yi = oo * oo), Ps = Ia * (os = (Mi = Math.sqrt(ki)) * w / Li) / Hr, ws = It < 0 ? 0 : Ps < It ? Ps : It, js = Zn + oo, ui = (hi = w * w) - (is = Hr * It / Ia) * is, dr = (xs = Zn + (ns = (vi = Math.sqrt(ui)) * Zn / w)) - (ls = (ss - oo) / 2), Jt = (vs = js + ns) + ls, Ls = Nt - (ds = ss / 2), Rs = Qr + is, ps = -(Jn = Math.atan(ns / is)), Pi = oo / 2, fs = (cs = Math.atan(Pi / os)) - Jn, ms = Math.PI / 2 - Jn, Ke = Jn + cs, us = 180 * (hs = Math.PI / 2 - cs) / Math.PI, ys = 180 * fs / Math.PI, gs = 180 * ms / Math.PI, bs = 180 * Jn / Math.PI, g += "<path d='" + (zn = sl(Zn, 0, Zn, w, us, us + ys, !1) + " L" + xs + "," + Rs + " L" + dr + "," + Rs + " L" + Ls + "," + Qr + " L" + Jt + "," + Rs + " L" + vs + "," + Rs + sl(js, 0, Zn, w, gs, gs + bs, !1).replace("M", "L") + " L" + Zn + "," + Ft + sl(Zn, 0, Zn, w, sa, ia, !1).replace("M", "L") + " L" + oo + "," + Qr + sl(js, 0, Zn, w, ia, sa, !1).replace("M", "L")) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "mathDivide": case "mathEqual": case "mathMinus": case "mathMultiply": case "mathNotEqual": case "mathPlus": if (void 0 !== (ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) if (ha.constructor === Array) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4))) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4))) : "adj3" == to && (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4))); else Zs = ul(ha, ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)); var fs, us, vs, Ls, gs, bs, ms, ys, ks, Ms, xs, Ps, ws, Is, _s, Cs, Ss, Ts, As, Ds, Gs, js, wa = 5e4 * tl, Ia = 1e5 * tl, zt = 2e5 * tl, z = P / 2, R = w / 2, Ca = w / 2; "mathNotEqual" == m ? (void 0 === ha ? (Sa = 23520 * tl, wt = 110 * Math.PI / 180, It = 11760 * tl) : (Sa *= tl, wt = wt / 6e4 * Math.PI / 180, It *= tl), _a = 70 * Math.PI / 180, Us = 110 * Math.PI / 180, Ps = Ia - 2 * (eo = Sa < 0 ? 0 : wa < Sa ? wa : Sa), Ds = z - (Ns = P * (Ie = 73490 * tl) / zt), Jt = z + Ns, Rs = (zs = R - (Os = w * (ws = It < 0 ? 0 : Ps < It ? Ps : It) / zt)) - (Ts = w * eo / Ia), pr = (Bs = R + Os) + Ts, Ms = (wt < _a ? _a : Us < wt ? Us : wt) - Math.PI / 2, ys = (vs = z + (fs = Ca * Math.tan(Ms)) - (us = (ms = Math.sqrt(fs * fs + Ca * Ca)) * Ts / Ca) / 2) + us, Gs = vs - 2 * fs, ks = Ts * Ca / ms, Hs = -(Es = Ts * fs / ms), gs = 0 < Ms ? vs + ks : ys, bs = 0 < Ms ? vs : ys - ks, _a = P - bs, Us = P - gs, ms = 0 < Ms ? Es : 0, ys = 0 < Ms ? 0 : Hs, ks = w - ys, Ms = w - ms, Us = "M" + Ds + "," + Rs + " L" + (Ls = vs - fs * Rs / Ca) + "," + Rs + " L" + bs + "," + ys + " L" + gs + "," + ms + " L" + (Ls + us) + "," + Rs + " L" + Jt + "," + Rs + " L" + Jt + "," + zs + " L" + ((xs = vs - fs * zs / Ca) + us) + "," + zs + " L" + ((dr = vs - fs * Bs / Ca) + us) + "," + Bs + " L" + Jt + "," + Bs + " L" + Jt + "," + pr + " L" + ((js = vs - fs * pr / Ca) + us) + "," + pr + " L" + _a + "," + ks + " L" + Us + "," + Ms + " L" + js + "," + pr + " L" + Ds + "," + pr + " L" + Ds + "," + Bs + " L" + dr + "," + Bs + " L" + xs + "," + zs + " L" + Ds + "," + zs + " z") : "mathDivide" == m ? (void 0 === ha ? (Sa = 23520 * tl, wt = 5880 * tl, It = 11760 * tl) : (Sa *= tl, wt *= tl, It *= tl), _s = 36745 * tl, Ps = (Ms = ((Ss = 73490 * tl) + -(eo = Sa < (Ie = 1e3 * tl) ? Ie : _s < Sa ? _s : Sa)) / 4) < (xs = _s * P / w) ? Ms : xs, xt = Ss + -4 * (ws = It < Ie ? Ie : Ps < It ? Ps : It) - eo, pr = R + (Ts = w * eo / zt), Is = w - (Rs = (zs = (Bs = R - Ts) - (Vs = w * (zo = wt < 0 ? 0 : xt < wt ? xt : wt) / Ia + (ws = w * ws / Ia))) - ws), Ds = z - (Ns = P * Ss / zt), js = z + Ns, Gs = z - ws, sa = 90, na = 270, qr = z - Math.cos(na * Math.PI / 180) * ws, Ur = Rs - Math.sin(na * Math.PI / 180) * ws, B = z - Math.cos(Math.PI / 2) * ws, O = Is - Math.sin(Math.PI / 2) * ws, Us = "M" + z + "," + Rs + sl(qr, Ur, ws, ws, na, na + 360, !1).replace("M", "L") + " z M" + z + "," + Is + sl(B, O, ws, ws, sa, sa + 360, !1).replace("M", "L") + " z M" + Ds + "," + Bs + " L" + js + "," + Bs + " L" + js + "," + pr + " L" + Ds + "," + pr + " z") : "mathEqual" == m ? (void 0 === ha ? (Sa = 23520 * tl, wt = 11760 * tl) : (Sa *= tl, wt *= tl), _s = 36745 * tl, Cs = Ia - 2 * (eo = Sa < 0 ? 0 : _s < Sa ? _s : Sa), Rs = (zs = R - (Os = w * (zo = wt < 0 ? 0 : Cs < wt ? Cs : wt) / zt)) - (Ts = w * eo / Ia), Bs = R + Os, pr = Bs + Ts, Us = "M" + (Ds = z - (Ns = P * (Ss = 73490 * tl) / zt)) + "," + Rs + " L" + (Gs = z + Ns) + "," + Rs + " L" + Gs + "," + zs + " L" + Ds + "," + zs + " zM" + Ds + "," + Bs + " L" + Gs + "," + Bs + " L" + Gs + "," + pr + " L" + Ds + "," + pr + " z") : "mathMinus" == m ? (void 0 === ha ? Sa = 23520 * tl : Sa *= tl, Us = "M" + (Ds = z - (Ns = P * (Ss = 73490 * tl) / zt)) + "," + (Rs = R - (Ts = w * (eo = Sa < 0 ? 0 : Ia < Sa ? Ia : Sa) / zt)) + " L" + (Gs = z + Ns) + "," + Rs + " L" + Gs + "," + (zs = R + Ts) + " L" + Ds + "," + zs + " z") : "mathMultiply" == m ? (void 0 === ha ? Sa = 23520 * tl : Sa *= tl, Ss = 51965 * tl, oo = (Hr = Math.min(P, w)) * (eo = Sa < 0 ? 0 : Ss < Sa ? Ss : Sa) / Ia, Vs = Math.atan(w / P), gn = +Math.sin(Vs), Qo = +Math.cos(Vs), yo = +Math.tan(Vs), Ho = Qo * (Jo = ($o = Math.sqrt(P * P + w * w)) - $o * Ss / Ia) / 2, Fi = gn * Jo / 2, Us = "M" + (Oo = Ho - (Cs = gn * oo / 2)) + "," + (Eo = Fi + (Zo = Qo * oo / 2)) + " L" + ($o = Ho + Cs) + "," + (Jo = Fi - Zo) + " L" + z + "," + (gn = (z - $o) * yo + Jo) + " L" + (Qo = P - $o) + "," + Jo + " L" + (Ho = P - Oo) + "," + Eo + " L" + (Fi = Ho - (Cs = (R - Eo) / yo)) + "," + R + " L" + Ho + "," + (Zo = w - Eo) + " L" + Qo + "," + (yo = w - Jo) + " L" + z + "," + (w - gn) + " L" + $o + "," + yo + " L" + Oo + "," + Zo + " L" + (Oo + Cs) + "," + R + " z") : "mathPlus" == m && (void 0 === ha ? Sa = 23520 * tl : Sa *= tl, Us = "M" + (Ds = z - (Ns = P * (Ss = 73490 * tl) / zt)) + "," + (zs = R - (As = (Hr = Math.min(P, w)) * (eo = Sa < 0 ? 0 : Ss < Sa ? Ss : Sa) / zt)) + " L" + (Gs = z - As) + "," + zs + " L" + Gs + "," + (Rs = R - (Ts = w * Ss / zt)) + " L" + (js = z + As) + "," + Rs + " L" + js + "," + zs + " L" + (dr = z + Ns) + "," + zs + " L" + dr + "," + (Bs = R + As) + " L" + js + "," + Bs + " L" + js + "," + (pr = R + Ts) + " L" + Gs + "," + pr + " L" + Gs + "," + Bs + " L" + Ds + "," + Bs + " z"), g += "<path d='" + Us + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "can": case "flowChartMagneticDisk": case "flowChartMagneticDrum": Ma = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), xa = 25e3 * tl, wa = 5e4 * tl, Ia = 2e5 * tl; void 0 !== Ma && (xa = parseInt(Ma.substr(4)) * tl); var Rs, zs, Bs, Hr = Math.min(P, w); "flowChartMagneticDisk" != m && "flowChartMagneticDrum" != m || (xa = 5e4 * tl), Aa = wa * w / Hr, zs = (Rs = Hr * (Vs = xa < 0 ? 0 : Aa < xa ? Aa : xa) / Ia) + Rs, Bs = w - Rs, g += "<path " + (ua = "flowChartMagneticDrum" == m ? "transform='rotate(90 " + P / 2 + "," + w / 2 + ")'" : "") + " d='" + (Us = sl(oa = P / 2, Rs, oa, Rs, 0, ia = 180, !1) + sl(oa, Rs, oa, Rs, ia, ia + ia, !1).replace("M", "L") + " L" + P + "," + Bs + sl(oa, Bs, oa, Rs, 0, ia, !1).replace("M", "L") + " L0," + Rs) + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "swooshArrow": var Fs, ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 25e3 * (Fs = tl), wt = 16667 * Fs; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * Fs) : "adj2" == to && ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) * Fs); var Ns, Os, Es, Hs, qs, Us, wa = +Fs, zt = 75e3 * Fs, Ie = 1e5 * Fs, Vs = w / 6; xt = (Ia = 7e4 * Fs) * P / (Hr = Math.min(P, w)), zt = w * (eo = Sa < wa ? wa : zt < Sa ? zt : Sa) / Ie, $o = P - Hr * (zo = wt < 0 ? 0 : xt < wt ? xt : wt) / Ie, Jo = Xe = Hr / 8, Ie = Math.PI / 2 / 14, g += "<path d='" + (Us = "M0," + w + " Q" + P / 6 + "," + ((Hs = Vs) + Hs) + " " + $o + "," + Jo + " L" + (Ln = $o - (Hs = Xe * Math.tan(Ie))) + ",0 L" + P + "," + (Xo = (Os = +(qo = (Ni = Jo + zt) + Xe)) / 2 - (Es = w / 20)) + " L" + (Ho = (Fi = $o + (Ns = zt * Math.tan(Ie))) + Hs) + "," + qo + " L" + Fi + "," + Ni + " Q" + P / 4 + "," + (Ni + (qs = Vs / 2)) + " 0," + w + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "circularArrow": ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 12500 * tl, wt = 19.03865 * Math.PI / 180, It = 340.96135 * Math.PI / 180, fe = 180 * Math.PI / 180, ue = 12500 * tl; if (void 0 !== ha) for (fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) / 6e4 * Math.PI / 180) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) / 6e4 * Math.PI / 180) : "adj4" == to ? (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) / 6e4 * Math.PI / 180) : "adj5" == to && (ao = ul(ha[fa], ["attrs", "fmla"]), ue = parseInt(ao.substr(4)) * tl); var R = w / 2, z = P / 2, Nt = P, Ft = w, Xr = 0, Qr = 0, oa = P / 2, Ca = w / 2, Hr = Math.min(P, w), wa = 25e3 * tl, Ia = 1e5 * tl, Qs = 1 / 6e4 * Math.PI / 180, Xs = 21599999 / 6e4 * Math.PI / 180, Ys = 2 * Math.PI; Pt = 2 * (ro = ue < 0 ? 0 : wa < ue ? wa : ue), so = It < Qs ? Qs : Xs < It ? Xs : It, $n = fe < 0 ? 0 : Xs < fe ? Xs : fe, uo = (ho = (po = Ca + (no = (oo = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / Ia) / 2) - (io = Hr * ro / Ia)) - oo) + no, vo = (fo = (co = (lo = oa + no - io) - oo) + no) * Math.sin(so), Lo = uo * Math.cos(so), mo = z + (go = fo * Math.cos(Math.atan2(vo, Lo))), yo = R + (bo = uo * Math.sin(Math.atan2(vo, Lo))), _o = 1 - (Io = (wo = (Mo = go * go) - (Po = (ko = co < ho ? co : ho) * ko)) * ((xo = bo * bo) - Po) / Mo / xo), Co = (1 + Math.sqrt(_o)) / (wo / go / bo), To = (So = Math.atan2(Co, 1)) + Ys, Do = (Ao = (0 < So ? So : To) - so) + Ys, jo = (Go = 0 < Ao ? Ao : Do) - Ys, Ro = 0 < Go - ia ? jo : Go, Ws = Math.abs(Ro), Bo = so + (wt < 0 ? 0 : Ws < wt ? Ws : wt), Fo = fo * Math.sin(Bo), No = uo * Math.cos(Bo), Oo = z + fo * Math.cos(Math.atan2(Fo, No)), Eo = R + uo * Math.sin(Math.atan2(Fo, No)), Uo = lo * Math.sin($n), Vo = po * Math.cos($n), Ho = z + lo * Math.cos(Math.atan2(Uo, Vo)), qo = R + po * Math.sin(Math.atan2(Uo, Vo)), Yo = io * Math.cos(Bo), Zo = yo + io * Math.sin(Bo), li = (ii = (ti = (Wo = mo + Yo) - z) * (ei = lo < po ? lo : po) / lo) - (si = (Ko = ($o = mo - io * Math.cos(Bo)) - z) * ei / lo), di = (ni = (ri = Zo - R) * ei / po) - (oi = (ai = (Jo = yo - io * Math.sin(Bo)) - R) * ei / po), gi = 0 < (Li = (vi = ei * ei * (ui = (pi = Math.sqrt(li * li + di * di)) * pi)) - (fi = (ci = si * ni) - (hi = ii * oi)) * fi) ? Li : 0, Ti = ii - (xi = ((Mi = fi * di) + (ki = (yi = (mi = 0 < -1 * di ? -1 : 1) * li) * (bi = Math.sqrt(gi)))) / ui), Ai = ii - (wi = (Pi = Mi - ki) / ui), Di = ni - (Ci = ((_i = fi * li / -1) + (Ii = Math.abs(di) * bi)) / ui), Gi = ni - (Si = (_i - Ii) / ui), ji = Math.sqrt(Ti * Ti + Di * Di), Fi = z + (zi = (0 < (Ri = Math.sqrt(Ai * Ai + Gi * Gi) - ji) ? xi : wi) * lo / ei), Ni = R + (Bi = (0 < Ri ? Ci : Si) * po / ei), Ui = (Hi = ti * ko / co) - (Oi = Ko * ko / co), Vi = (qi = ri * ko / ho) - (Ei = ai * ko / ho), $i = 0 < (Zi = ko * ko * (Wi = (Qi = Math.sqrt(Ui * Ui + Vi * Vi)) * Qi) - (Yi = Oi * qi - (Xi = Hi * Ei)) * Yi) ? Zi : 0, ln = Oi - (tn = ((an = Yi * Vi) + (Ki = mi * Ui * (Ji = Math.sqrt($i)))) / Wi), dn = Oi - (rn = (an - Ki) / Wi), pn = Ei - (on = ((sn = Yi * Ui / -1) + (en = Math.abs(Vi) * Ji)) / Wi), cn = Ei - (nn = (sn - en) / Wi), hn = Math.sqrt(ln * ln + pn * pn), Ln = z + (un = (0 < (fn = Math.sqrt(dn * dn + cn * cn) - hn) ? tn : rn) * co / ko), gn = R + (vn = (0 < fn ? on : nn) * ho / ko), mn = (bn = Math.atan2(vn, un)) + Ys, kn = (yn = $n - (Mn = 0 < bn ? bn : mn)) - Ys, xn = 0 < yn ? kn : yn, Pn = Fi - Ln, wn = Ni - gn, _n = 0 < (In = Math.sqrt(Pn * Pn + wn * wn) / 2 - io) ? Fi : Wo, Cn = 0 < In ? Ni : Zo, Sn = 0 < In ? Ln : $o, Tn = 0 < In ? gn : Jo, Dn = (An = Math.atan2(Bi, zi)) + Ys, jn = (Gn = (0 < An ? An : Dn) - $n) + Ys, Jn = 0 < Gn ? Gn : jn; var jt = (Bn = 180 * $n / Math.PI) + 180 * Jn / Math.PI, Ws = (Rn = 180 * Mn / Math.PI) + 180 * xn / Math.PI; g += "<path d='" + (zn = sl(P / 2, w / 2, lo, po, Bn, jt, !1) + " L" + _n + "," + Cn + " L" + Oo + "," + Eo + " L" + Sn + "," + Tn + " L" + Ln + "," + gn + sl(P / 2, w / 2, co, ho, Rn, Ws, !1).replace("M", "L") + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftCircularArrow": var Zs, $s, Js, Ks, ao, ha = ul(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Sa = 12500 * tl, wt = -19.03865 * Math.PI / 180, It = 19.03865 * Math.PI / 180, fe = 180 * Math.PI / 180, ue = 12500 * tl; if (void 0 !== ha) for (var to, fa = 0; fa < ha.length; fa++)"adj1" == (to = ul(ha[fa], ["attrs", "name"])) ? (Zs = ul(ha[fa], ["attrs", "fmla"]), Sa = parseInt(Zs.substr(4)) * tl) : "adj2" == to ? ($s = ul(ha[fa], ["attrs", "fmla"]), wt = parseInt($s.substr(4)) / 6e4 * Math.PI / 180) : "adj3" == to ? (Js = ul(ha[fa], ["attrs", "fmla"]), It = parseInt(Js.substr(4)) / 6e4 * Math.PI / 180) : "adj4" == to ? (Ks = ul(ha[fa], ["attrs", "fmla"]), fe = parseInt(Ks.substr(4)) / 6e4 * Math.PI / 180) : "adj5" == to && (ao = ul(ha[fa], ["attrs", "fmla"]), ue = parseInt(ao.substr(4)) * tl); var ro, eo, so, oo, io, no, lo, po, co, ho, fo, uo, vo, Lo, go, bo, mo, yo, ko, Mo, xo, Po, wo, Io, _o, Co, So, To, Ao, Do, Go, jo, Ro, zo, Bo, Fo, No, Oo, Eo, Ho, qo, Uo, Vo, Qo, Xo, Yo, Wo, Zo, $o, Jo, Ko, ai, ti, ri, ei, si, oi, ii, ni, li, di, pi, ci, hi, fi, ui, vi, Li, gi, bi, mi, yi, ki, Mi, xi, Pi, wi, Ii, _i, Ci, Si, Ti, Ai, Di, Gi, ji, Ri, zi, Bi, Fi, Ni, Oi, Ei, Hi, qi, Ui, Vi, Qi, Xi, Yi, Wi, Zi, $i, Ji, Ki, an, tn, rn, en, sn, on, nn, ln, dn, pn, cn, hn, fn, un, vn, Ln, gn, bn, mn, yn, kn, Mn, xn, Pn, wn, In, _n, Cn, Sn, Tn, An, Dn, Gn, jn, R = w / 2, z = P / 2, Nt = P, Ft = w, Xr = 0, Qr = 0, oa = P / 2, Ca = w / 2, Hr = Math.min(P, w), wa = 25e3 * tl, Ia = 1e5 * tl, Qs = 1 / 6e4 * Math.PI / 180, Xs = 21599999 / 6e4 * Math.PI / 180, Ys = 2 * Math.PI; Pt = 2 * (ro = ue < 0 ? 0 : wa < ue ? wa : ue), so = It < Qs ? Qs : Xs < It ? Xs : It, $n = fe < 0 ? 0 : Xs < fe ? Xs : fe, uo = (ho = (po = Ca + (no = (oo = Hr * (eo = Sa < 0 ? 0 : Pt < Sa ? Pt : Sa) / Ia) / 2) - (io = Hr * ro / Ia)) - oo) + no, vo = (fo = (co = (lo = oa + no - io) - oo) + no) * Math.sin(so), Lo = uo * Math.cos(so), mo = z + (go = fo * Math.cos(Math.atan2(vo, Lo))), yo = R + (bo = uo * Math.sin(Math.atan2(vo, Lo))), _o = 1 - (Io = (wo = (Mo = go * go) - (Po = (ko = co < ho ? co : ho) * ko)) * ((xo = bo * bo) - Po) / Mo / xo), Co = (1 + Math.sqrt(_o)) / (wo / go / bo), To = (So = Math.atan2(Co, 1)) + Ys, Do = (Ao = (0 < So ? So : To) - so) + Ys, jo = (Go = 0 < Ao ? Ao : Do) - Ys, Ro = 0 < Go - ia ? jo : Go, Ro = -1 * Math.abs(Ro), Bo = so + ((zo = -1 * Math.abs(wt)) < Ro ? Ro : 0 < zo ? 0 : zo), Fo = fo * Math.sin(Bo), No = uo * Math.cos(Bo), Oo = z + fo * Math.cos(Math.atan2(Fo, No)), Eo = R + uo * Math.sin(Math.atan2(Fo, No)), Uo = lo * Math.sin($n), Vo = po * Math.cos($n), Ho = z + lo * Math.cos(Math.atan2(Uo, Vo)), qo = R + po * Math.sin(Math.atan2(Uo, Vo)), Uo = co * Math.sin($n), Vo = ho * Math.cos($n), Qo = z + co * Math.cos(Math.atan2(Uo, Vo)), Xo = R + ho * Math.sin(Math.atan2(Uo, Vo)), Yo = io * Math.cos(Bo), Zo = yo + io * Math.sin(Bo), li = (ii = (ti = (Wo = mo + Yo) - z) * (ei = lo < po ? lo : po) / lo) - (si = (Ko = ($o = mo - io * Math.cos(Bo)) - z) * ei / lo), di = (ni = (ri = Zo - R) * ei / po) - (oi = (ai = (Jo = yo - io * Math.sin(Bo)) - R) * ei / po), gi = 0 < (Li = (vi = ei * ei * (ui = (pi = Math.sqrt(li * li + di * di)) * pi)) - (fi = (ci = si * ni) - (hi = ii * oi)) * fi) ? Li : 0, Ti = ii - (xi = ((Mi = fi * di) + (ki = (yi = (mi = 0 < -1 * di ? -1 : 1) * li) * (bi = Math.sqrt(gi)))) / ui), Ai = ii - (wi = (Pi = Mi - ki) / ui), Di = ni - (Ci = ((_i = fi * li / -1) + (Ii = Math.abs(di) * bi)) / ui), Gi = ni - (Si = (_i - Ii) / ui), ji = Math.sqrt(Ti * Ti + Di * Di), Fi = z + (zi = (0 < (Ri = Math.sqrt(Ai * Ai + Gi * Gi) - ji) ? xi : wi) * lo / ei), Ni = R + (Bi = (0 < Ri ? Ci : Si) * po / ei), Ui = (Hi = ti * ko / co) - (Oi = Ko * ko / co), Vi = (qi = ri * ko / ho) - (Ei = ai * ko / ho), $i = 0 < (Zi = ko * ko * (Wi = (Qi = Math.sqrt(Ui * Ui + Vi * Vi)) * Qi) - (Yi = Oi * qi - (Xi = Hi * Ei)) * Yi) ? Zi : 0, ln = Oi - (tn = ((an = Yi * Vi) + (Ki = mi * Ui * (Ji = Math.sqrt($i)))) / Wi), dn = Oi - (rn = (an - Ki) / Wi), pn = Ei - (on = ((sn = Yi * Ui / -1) + (en = Math.abs(Vi) * Ji)) / Wi), cn = Ei - (nn = (sn - en) / Wi), hn = Math.sqrt(ln * ln + pn * pn), Ln = z + (un = (0 < (fn = Math.sqrt(dn * dn + cn * cn) - hn) ? tn : rn) * co / ko), gn = R + (vn = (0 < fn ? on : nn) * ho / ko), mn = (bn = Math.atan2(vn, un)) + Ys, kn = (yn = $n - (mn = 0 < bn ? bn : mn)) + Ys, Mn = mn + (kn = 0 < yn ? yn : kn), xn = -kn, Pn = Fi - Ln, wn = Ni - gn, _n = 0 < (In = Math.sqrt(Pn * Pn + wn * wn) / 2 - io) ? Fi : Wo, Cn = 0 < In ? Ni : Zo, Sn = 0 < In ? Ln : $o, Tn = 0 < In ? gn : Jo, Dn = (An = Math.atan2(Bi, zi)) + Ys, jn = (Gn = (0 < An ? An : Dn) - $n) - Ys; var Rn, zn, Bn = 180 * ($n + (Jn = 0 < Gn ? jn : Gn)) / Math.PI, jt = 180 * $n / Math.PI; g += "<path d='" + (zn = "M" + Ho + "," + qo + " L" + Qo + "," + Xo + sl(P / 2, w / 2, co, ho, Rn = 180 * Mn / Math.PI, Ws = Rn + 180 * xn / Math.PI, !1).replace("M", "L") + " L" + Sn + "," + Tn + " L" + Oo + "," + Eo + " L" + _n + "," + Cn + " L" + Fi + "," + Ni + sl(P / 2, w / 2, lo, po, Bn, jt, !1).replace("M", "L") + " z") + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + S.color + "' stroke-width='" + S.width + "' stroke-dasharray='" + S.strokeDasharray + "' />"; break; case "leftRightCircularArrow": case "chartPlus": case "chartStar": case "chartX": case "cornerTabs": case "flowChartOfflineStorage": case "folderCorner": case "funnel": case "lineInv": case "nonIsoscelesTrapezoid": case "plaqueTabs": case "squareTabs": case "upDownArrowCallout": console.log(m, " -unsupported shape type."); break; case void 0: default: console.warn("Undefine shape type.(" + m + ")") }g += "</svg>", g += "<div class='block " + ll(a, r, e) + " " + dl() + "' _id='" + s + "' _idx='" + i + "' _type='" + n + "' _name='" + o + "' style='" + il(u, t, v, L, c) + nl(u, v, L) + " z-index: " + l + ";transform: rotate(" + (void 0 !== x ? x : 0) + "deg);'>", void 0 === a["p:txBody"] || void 0 !== p && !0 !== p || ("diagram" != n && "textBox" != n && (n = "shape"), g += ol(a["p:txBody"], a, r, 0, n, i, d)), g += "</div>" } else if (void 0 !== y) { var Fn = ul(y, ["a:pathLst"]), Nn = ul(Fn, ["a:path"]), On = 1 / parseInt(Nn.attrs.w) * P, En = 1 / parseInt(Nn.attrs.h) * w, Hn = ul(Nn, ["a:moveTo"]), qn = (Hn.length, Nn["a:lnTo"]), Un = Nn["a:cubicBezTo"], Vn = Nn["a:arcTo"], Qn = ul(Nn, ["a:close"]); Array.isArray(Hn) || (Hn = [Hn]); var Xn, Yn = []; if (0 < Hn.length) { Object.keys(Hn).forEach(function (a) { var s = Hn[a]["a:pt"]; void 0 !== s && Object.keys(s).forEach(function (a) { var t = {}, r = s[a], e = r.x, a = r.y, r = r.order; t.type = "movto", t.order = r, t.x = e, t.y = a, Yn.push(t) }) }), void 0 !== qn && Object.keys(qn).forEach(function (a) { var s = qn[a]["a:pt"]; void 0 !== s && Object.keys(s).forEach(function (a) { var t = {}, r = s[a], e = r.x, a = r.y, r = r.order; t.type = "lnto", t.order = r, t.x = e, t.y = a, Yn.push(t) }) }), void 0 !== Un && (Xn = [], Array.isArray(Un) || (Un = [Un]), Object.keys(Un).forEach(function (a) { Xn.push(Un[a]["a:pt"]) }), Xn.forEach(function (a) { var t = { type: "cubicBezTo" }; t.order = a[0].attrs.order; var r = []; a.forEach(function (a) { a = { x: a.attrs.x, y: a.attrs.y }; r.push(a) }), t.cubBzPt = r, Yn.push(t) })), void 0 !== Vn && (Fn = (y = Vn.attrs).order, Wn = y.hR, Zn = y.wR, $n = y.stAng, Jn = y.swAng, void (y = Nn = 0) !== (Vn = ul(Vn, ["a:pt", "attrs"])) && (Nn = Vn.x, y = Vn.y), (Vn = { type: "arcTo" }).order = Fn, Vn.hR = Wn, Vn.wR = Zn, Vn.stAng = $n, Vn.swAng = Jn, Vn.shftX = Nn, Vn.shftY = y, Yn.push(Vn)), void 0 !== Qn && (Array.isArray(Qn) || (Qn = [Qn]), Object.keys(Qn).forEach(function (a) { var t = Qn[a].attrs.order, a = { type: "close" }; a.order = t, Yn.push(a) })), Yn.sort(function (a, t) { return a.order - t.order }); for (var Wn, Zn, $n, Jn, Kn = 0, al = !1, ga = ""; Kn < Yn.length;)"movto" == Yn[Kn].type ? ga += " M" + parseInt(Yn[Kn].x) * On + "," + parseInt(Yn[Kn].y) * En : "lnto" == Yn[Kn].type ? ga += " L" + parseInt(Yn[Kn].x) * On + "," + parseInt(Yn[Kn].y) * En : "cubicBezTo" == Yn[Kn].type ? ga += " C" + parseInt(Yn[Kn].cubBzPt[0].x) * On + "," + parseInt(Yn[Kn].cubBzPt[0].y) * En + " " + parseInt(Yn[Kn].cubBzPt[1].x) * On + "," + parseInt(Yn[Kn].cubBzPt[1].y) * En + " " + parseInt(Yn[Kn].cubBzPt[2].x) * On + "," + parseInt(Yn[Kn].cubBzPt[2].y) * En : "arcTo" == Yn[Kn].type ? (Wn = parseInt(Yn[Kn].hR) * On, ga += sl(Zn = parseInt(Yn[Kn].wR) * En, Wn, Zn, Wn, $n = parseInt(Yn[Kn].stAng) / 6e4, jt = $n + (Jn = parseInt(Yn[Kn].swAng) / 6e4), !1)) : "quadBezTo" == Yn[Kn].type ? console.log("custShapType: quadBezTo - TODO") : "close" == Yn[Kn].type && (ga += "z"), Kn++; g += "<path d='" + ga + "' fill='" + (C ? "url(#imgPtrn_" + b + ")" : _ ? "url(#linGrd_" + b + ")" : I) + "' stroke='" + (void 0 === S ? "" : S.color) + "' stroke-width='" + (void 0 === S ? "" : S.width) + "' stroke-dasharray='" + (void 0 === S ? "" : S.strokeDasharray) + "' ", g += "/>" } g += "</svg>", g += "<div class='block " + ll(a, r, e) + " " + dl() + "' _id='" + s + "' _idx='" + i + "' _type='" + n + "' _name='" + o + "' style='" + il(u, t, v, L, c) + nl(u, v, L) + " z-index: " + l + ";transform: rotate(" + (void 0 !== x ? x : 0) + "deg);'>", void 0 === a["p:txBody"] || void 0 !== p && !0 !== p || ("diagram" != n && "textBox" != n && (n = "shape"), g += ol(a["p:txBody"], a, r, 0, n, i, d)), g += "</div>" } else g += "<div class='block " + ll(a, r, e) + " " + dl() + "' _id='" + s + "' _idx='" + i + "' _type='" + n + "' _name='" + o + "' style='" + il(u, t, v, L, c) + nl(u, v, L) + pl(a, 0, !1, "shape", d) + cl(a, t, !1, d, h) + " z-index: " + l + ";transform: rotate(" + (void 0 !== x ? x : 0) + "deg);'>", void 0 === a["p:txBody"] || void 0 !== p && !0 !== p || (g += ol(a["p:txBody"], a, r, 0, n, i, d)), g += "</div>"; return g } function el(t, r, e) { var s = 1.5 * r, o = s; cy = s, notches = e, radiusO = s, radiusI = r, taperO = 50, taperI = 35, pi2 = 2 * Math.PI, angle = pi2 / (2 * notches), taperAI = angle * taperI * .005, taperAO = angle * taperO * .005, a = angle, toggle = !1; for (var i = " M" + (o + radiusO * Math.cos(taperAO)) + " " + (cy + radiusO * Math.sin(taperAO)); a <= pi2 + angle; a += angle)toggle ? (i += " L" + (o + radiusI * Math.cos(a - taperAI)) + "," + (cy + radiusI * Math.sin(a - taperAI)), i += " L" + (o + radiusO * Math.cos(a + taperAO)) + "," + (cy + radiusO * Math.sin(a + taperAO))) : (i += " L" + (o + radiusO * Math.cos(a - taperAO)) + "," + (cy + radiusO * Math.sin(a - taperAO)), i += " L" + (o + radiusI * Math.cos(a + taperAI)) + "," + (cy + radiusI * Math.sin(a + taperAI))), toggle = !toggle; return i += " " } function sl(a, t, r, e, s, o, i) { var n, l = s; if (s <= o) for (; l <= o;) { var d = l * (Math.PI / 180), p = a + Math.cos(d) * r, c = t + Math.sin(d) * e; l == s && (n = " M" + p + " " + c), n += " L" + p + " " + c, l++ } else for (; o < l;) { d = l * (Math.PI / 180), p = a + Math.cos(d) * r, c = t + Math.sin(d) * e; l == s && (n = " M " + p + " " + c), n += " L " + p + " " + c, l-- } return n += i ? " z" : "" } "" != D.fileInputId && ta("#" + D.fileInputId).on("change", function (a) { d.html(""); a = a.target.files[0]; "application/vnd.openxmlformats-officedocument.presentationml.presentation" == a.type ? FileReaderJS.setupBlob(a, { readAsDefault: "ArrayBuffer", on: { load: function (a, t) { s(a.target.result) } } }) : alert("This is not pptx file") }); var I = !1; function ol(a, t, r, e, s, o, i, n) { var l = ""; i.slideMasterTextStyles; if (void 0 === a) return l; var d = ul(t, ["p:style", "a:fontRef"]), p = a["a:p"]; p.constructor !== Array && (p = [p]); for (var c = 0; c < p.length; c++) { var h = p[c], f = h["a:r"], u = h["a:fld"], v = h["a:br"]; void 0 !== f && (f = f.constructor === Array ? f : [f]), void 0 !== f && void 0 !== u && (u = u.constructor === Array ? u : [u], f = f.concat(u)), void 0 !== f && void 0 !== v && (I = !0, (v = v.constructor === Array ? v : [v]).forEach(function (a, t) { a.type = "br" }), 1 < v.length && v.shift(), (f = f.concat(v)).sort(function (a, t) { return a.attrs.order - t.attrs.order })); var L = "", g = function (a, t, r, e, s) { var o = 1, i = ul(a, ["a:pPr", "a:spcBef", "a:spcPts", "attrs", "val"]), n = ul(a, ["a:pPr", "a:spcAft", "a:spcPts", "attrs", "val"]), l = ul(a, ["a:pPr", "a:lnSpc", "a:spcPct", "attrs", "val"]), d = "Pct"; void 0 === l && void 0 !== (l = ul(a, ["a:pPr", "a:lnSpc", "a:spcPts", "attrs", "val"])) && (d = "Pts"); var p, c = ul(a, ["a:pPr", "attrs", "lvl"]); void 0 !== c && (o = parseInt(c) + 1); void 0 === ul(a, ["a:r"]) || "inherit" != (h = N(a["a:r"], t, 0, o, r, s)) && (p = parseInt(h, "px")); var h = !0; "shape" != r && "textBox" != r || (h = !1); !h || void 0 !== i && void 0 !== n && void 0 !== l || void 0 !== e && (e = ul(s, ["slideLayoutTables", "idxTable", e, "p:txBody", "a:p", o - 1, "a:pPr"]), void 0 === i && (i = ul(e, ["a:spcBef", "a:spcPts", "attrs", "val"])), void 0 === n && (n = ul(e, ["a:spcAft", "a:spcPts", "attrs", "val"])), void 0 === l && void 0 === (l = ul(e, ["a:lnSpc", "a:spcPct", "attrs", "val"])) && void 0 !== (l = ul(e, ["a:pPr", "a:lnSpc", "a:spcPts", "attrs", "val"])) && (d = "Pts")); if (h && (void 0 === i || void 0 === n || void 0 === l)) { var f = s.slideMasterTextStyles, u = "", o = "a:lvl" + o + "pPr"; switch (r) { case "title": case "ctrTitle": u = "p:titleStyle"; break; case "body": case "obj": case "dt": case "ftr": case "sldNum": case "textBox": u = "p:bodyStyle"; break; case "shape": default: u = "p:otherStyle" }var v = ul(f, [u, o]); void 0 !== v && (void 0 === i && (i = ul(v, ["a:spcBef", "a:spcPts", "attrs", "val"])), void 0 === n && (n = ul(v, ["a:spcAft", "a:spcPts", "attrs", "val"])), void 0 === l && void 0 === (l = ul(v, ["a:lnSpc", "a:spcPct", "attrs", "val"])) && void 0 !== (l = ul(v, ["a:pPr", "a:lnSpc", "a:spcPts", "attrs", "val"])) && (d = "Pts")) } r = 0, f = 0, o = 0, v = ""; void 0 !== i && (r = parseInt(i) / 100); void 0 !== n && (f = parseInt(n) / 100); void 0 !== l && void 0 !== p && ("Pts" == d ? v += "padding-top: " + (parseInt(l) / 100 - p) + "px;" : (d = parseInt(l) / 1e5, o = p * (d - 1) - p, v += "padding-top: " + (1 < d ? p : 0) + "px;", v += "padding-bottom: " + o + "px;")); v += "margin-top: " + (r - 1) + "px;", void 0 === n && void 0 === l || (v += "margin-bottom: " + f + "px;"); return v }(h, a, s, o, i); "" != g && (L = g), "body" != s && "obj" != s && "shape" != s || (L += "font-size: 0px;", L += "font-weight: 100;", L += "font-style: normal;"); u = ""; L in rl ? u = rl[L].name : (u = "_css_" + (Object.keys(rl).length + 1), rl[L] = { name: u, text: L }); v = ul(t, ["p:spPr", "a:xfrm", "a:ext", "attrs", "cx"]), g = void 0 !== v ? "width:" + parseInt(v) * tl + "px;" : "width:inherit;", L = function (a, t, r, e) { var s = ul(a, ["a:pPr", "attrs", "rtl"]); void 0 === s && (t = B(a, t, r, e), e = t.nodeLaout, t = t.nodeMaster, void 0 === (s = ul(e, ["attrs", "rtl"])) && "shape" != r && (s = ul(t, ["attrs", "rtl"]))); { if ("1" == s) return "pregraph-rtl"; if ("0" == s) return "pregraph-ltr" } return "pregraph-inherit" }(h, o, s, i); l += "<div style='display: flex;" + g + "' class='slide-prgrph " + function (a, t, r, e, s, o) { var i = ul(a, ["a:pPr", "attrs", "algn"]); { var n; void 0 === i && (n = 1, void 0 !== (a = ul(a, ["a:pPr", "attrs", "lvl"])) && (n = parseInt(a) + 1), a = "a:lvl" + n + "pPr", t = t["a:lstStyle"], void 0 === (i = ul(t, [a, "attrs", "algn"])) && void 0 !== r && void 0 === (i = ul(o.slideLayoutTables.idxTable[r], ["p:txBody", "a:lstStyle", a, "attrs", "algn"])) && void 0 === (i = ul(o.slideLayoutTables.idxTable[r], ["p:txBody", "a:p", "a:pPr", "attrs", "algn"])) && (i = ul(o.slideLayoutTables.idxTable[r], ["p:txBody", "a:p", n - 1, "a:pPr", "attrs", "algn"])), void 0 === i && (void 0 !== e ? void 0 === (i = ul(o, ["slideLayoutTables", "typeTable", e, "p:txBody", "a:lstStyle", a, "attrs", "algn"])) && (i = ul(o, "title" == e || "ctrTitle" == e ? ["slideMasterTextStyles", "p:titleStyle", a, "attrs", "algn"] : "body" == e || "obj" == e || "subTitle" == e ? ["slideMasterTextStyles", "p:bodyStyle", a, "attrs", "algn"] : "shape" == e || "diagram" == e ? ["slideMasterTextStyles", "p:otherStyle", a, "attrs", "algn"] : "textBox" == e ? ["defaultTextStyle", a, "attrs", "algn"] : ["slideMasterTables", "typeTable", e, "p:txBody", "a:lstStyle", a, "attrs", "algn"])) : i = ul(o, ["slideMasterTextStyles", "p:bodyStyle", a, "attrs", "algn"]))) } if (void 0 === i) { if ("title" == e || "subTitle" == e || "ctrTitle" == e) return "h-mid"; if ("sldNum" == e) return "h-right" } if (void 0 !== i) switch (i) { case "l": return "pregraph-rtl" == s ? "h-left-rtl" : "h-left"; case "r": return "pregraph-rtl" == s ? "h-right-rtl" : "h-right"; case "ctr": return "h-mid"; case "just": case "dist": default: return "h-" + i } }(h, a, o, s, L, i) + " " + L + " " + u + "' >"; var g = function (a, t, r, e, s, o, i, n) { n.slideMasterTextStyles; var l = e["a:lstStyle"], d = ul(a, ["a:r"]); void 0 !== d && d.constructor === Array && (d = d[0]); var p = parseInt(ul(a["a:pPr"], ["attrs", "lvl"])) + 1; isNaN(p) && (p = 1); var c, h, f, u, v = "a:lvl" + p + "pPr"; { if (void 0 === d) return ""; c = F(d, r, l, s, p, o, i, n), G = c[2], h = N(d, e, 0, p, i, n) } var L = "", g = "", b = "", m = 0, y = 0, k = a["a:pPr"], M = ul(k, ["a:buNone"]); if (void 0 !== M) return ""; var x = "TYPE_NONE", P = B(a, o, i, n), w = P.nodeLaout, I = P.nodeMaster, r = ul(k, ["a:buChar", "attrs", "char"]), d = ul(k, ["a:buAutoNum", "attrs", "type"]), e = ul(k, ["a:buBlip"]); void 0 !== r && (x = "TYPE_BULLET"); void 0 !== d && (x = "TYPE_NUMERIC"); void 0 !== e && (x = "TYPE_BULPIC"); a = ul(k, ["a:buSzPts", "attrs", "val"]); void 0 === a ? void 0 !== (a = ul(k, ["a:buSzPct", "attrs", "val"])) && (S = parseInt(a) / 1e5, T = parseInt(h, "px"), u = S * parseInt(T) + "px") : u = parseInt(a) / 100 * R + "px"; o = ul(k, ["a:buClr"]); if (void 0 === r && void 0 === d && void 0 === e && void 0 !== l) { if (void 0 !== (M = ul(l, [v, "a:buNone"]))) return ""; x = "TYPE_NONE", r = ul(l, [v, "a:buChar", "attrs", "char"]), d = ul(l, [v, "a:buAutoNum", "attrs", "type"]), e = ul(l, [v, "a:buBlip"]), void 0 !== r && (x = "TYPE_BULLET"), void 0 !== d && (x = "TYPE_NUMERIC"), void 0 !== e && (x = "TYPE_BULPIC"), void 0 === r && void 0 === d && void 0 === e || (k = l[v]) } if (void 0 === r && void 0 === d && void 0 === e) { if (void 0 !== w) { if (void 0 !== (M = ul(w, ["a:buNone"]))) return ""; x = "TYPE_NONE", r = ul(w, ["a:buChar", "attrs", "char"]), d = ul(w, ["a:buAutoNum", "attrs", "type"]), e = ul(w, ["a:buBlip"]), void 0 !== r && (x = "TYPE_BULLET"), void 0 !== d && (x = "TYPE_NUMERIC"), void 0 !== e && (x = "TYPE_BULPIC") } if (void 0 === r && void 0 === d && void 0 === e && void 0 !== I) { if (void 0 !== (M = ul(I, ["a:buNone"]))) return ""; x = "TYPE_NONE", r = ul(I, ["a:buChar", "attrs", "char"]), d = ul(I, ["a:buAutoNum", "attrs", "type"]), e = ul(I, ["a:buBlip"]), void 0 !== r && (x = "TYPE_BULLET"), void 0 !== d && (x = "TYPE_NUMERIC"), void 0 !== e && (x = "TYPE_BULPIC") } } P = ul(k, ["attrs", "rtl"]); void 0 === P && void 0 === (P = ul(w, ["attrs", "rtl"])) && "shape" != i && (P = ul(I, ["attrs", "rtl"])); M = !1; void 0 !== P && "1" == P && (M = !0); i = ul(k, ["attrs", "algn"]); void 0 === i && void 0 === (i = ul(w, ["attrs", "algn"])) && (i = ul(I, ["attrs", "algn"])); P = ul(k, ["attrs", "indent"]); void 0 === P && void 0 === (P = ul(w, ["attrs", "indent"])) && (P = ul(I, ["attrs", "indent"])); i = 0; void 0 !== P && (i = parseInt(P) * tl); P = ul(k, ["attrs", "marL"]); void 0 === P && void 0 === (P = ul(w, ["attrs", "marL"])) && (P = ul(I, ["attrs", "marL"])); void 0 !== P && (C = parseInt(P) * tl, b = M ? "padding-right:" : "padding-left:", b += (m = C + i < 0 ? 0 : C + i) + "px;"); var _, C = ul(k, ["attrs", "marR"]); void 0 === C && void 0 === P && void 0 === (C = ul(w, ["attrs", "marR"])) && (C = ul(I, ["attrs", "marR"])); void 0 !== C && (C = parseInt(C) * tl, b = M ? "padding-right:" : "padding-left:", g += (C + i < 0 ? 0 : C + i) + "px;"); void 0 === o && (o = ul(l, [v, "a:buClr"])); void 0 === o && void 0 === (o = ul(w, ["a:buClr"])) && (o = ul(I, ["a:buClr"])); void 0 !== o ? _ = fl(o, void 0, void 0, n) : void 0 !== s && (_ = fl(s, void 0, void 0, n)); void 0 === _ || "NONE" == _ ? f = c : (f = [_, "", "solid"], G = "solid"); void 0 === a && (void 0 === (a = ul(w, ["a:buSzPts", "attrs", "val"])) ? void 0 !== (a = ul(w, ["a:buSzPct", "attrs", "val"])) && (S = parseInt(a) / 1e5, T = parseInt(h, "px"), u = S * parseInt(T) + "px") : u = parseInt(a) / 100 * R + "px"); { var S, T; void 0 === a && (void 0 === (a = ul(I, ["a:buSzPts", "attrs", "val"])) ? void 0 !== (a = ul(I, ["a:buSzPct", "attrs", "val"])) && (S = parseInt(a) / 1e5, T = parseInt(h, "px"), u = S * parseInt(T) + "px") : u = parseInt(a) / 100 * R + "px") } void 0 === a && (u = h); if (y = parseInt(u, "px"), "TYPE_BULLET" == x) { k = ul(k, ["a:buFont", "attrs", "typeface"]); if (L = "<div style='height: 100%;" + (void 0 !== k ? "font-family: " + k : "") + ";" + b + g + "font-size:" + u + ";", "solid" == G) void 0 !== f[0] && "" != f[0] && (L += "color:#" + f[0] + "; "), void 0 !== f[1] && "" != f[1] && ";" != f[1] && (L += "text-shadow:" + f[1] + ";"); else if ("pattern" == G || "pic" == G || "gradient" == G) { if ("pattern" == G) L += "background:" + f[0][0] + ";", null !== f[0][1] && void 0 !== f[0][1] && "" != f[0][1] && (L += "background-size:" + f[0][1] + ";"), null !== f[0][2] && void 0 !== f[0][2] && "" != f[0][2] && (L += "background-position:" + f[0][2] + ";"); else if ("pic" == G) L += f[0] + ";"; else if ("gradient" == G) { var A = f[0].color, D = f[0].rot; L += "background: linear-gradient(" + D + "deg,"; for (t = 0; t < A.length; t++)t == A.length - 1 ? L += "#" + A[t] + ");" : L += "#" + A[t] + ", " } L += "-webkit-background-clip: text;background-clip: text;color: transparent;", void 0 !== f[1].border && "" !== f[1].border && (L += "-webkit-text-stroke: " + f[1].border + ";"), void 0 !== f[1].effcts && "" !== f[1].effcts && (L += "filter: " + f[1].effcts + ";") } M && (L += "white-space: nowrap ;direction:rtl"); var G = !!window.MSInputMethodContext && !!document.documentMode, D = r; G || (D = function (a, t) { switch (t) { case "§": return "&#9632;"; case "q": return "&#10065;"; case "v": return "&#10070;"; case "Ø": return "&#11162;"; case "ü": return "&#10004;"; default: if ("Wingdings 2" == a || "Wingdings 3" == a) { var r = function (a, t) { if (dingbat_unicode) { for (var r = 4095 & t.codePointAt(0), e = null, s = dingbat_unicode.length, o = 0; s--;) { var i = dingbat_unicode[o]; if (i.f == a && i.code == r) { e = i.unicode; break } o++ } return e } }(a, t); if (null !== r) return "&#" + r + ";" } return "&#" + t.charCodeAt(0) + ";" } }(k, r)), L += "'><div style='line-height: " + y / 2 + "px;'>" + D + "</div></div>" } else { var j; "TYPE_NUMERIC" == x ? (L = "<div style='height: 100%;" + b + g + "color:#" + f[0] + ";font-size:" + u + ";", L += M ? "display: inline-block;white-space: nowrap ;direction:rtl;" : "display: inline-block;white-space: nowrap ;direction:ltr;", L += "' data-bulltname = '" + d + "' data-bulltlvl = '" + p + "' class='numeric-bullet-style'></div>") : "TYPE_BULPIC" == x && (x = ul(e, ["a:blip", "attrs", "r:embed"]), void 0 !== x && (e = n.slideResObj[x].target, j = n.zip.file(e).asArrayBuffer(), e = X(e.split(".").pop()), j = "<img src='data:" + e + ";base64," + J(j) + "' style='width: 100%;'/>"), void 0 === x && (j = "&#8227;"), L = "<div style='height: 100%;" + b + g + "width:" + u + ";display: inline-block; ", M && (L += "display: inline-block;white-space: nowrap ;direction:rtl;"), L += "'>" + j + "  </div>") } return [L, m, y] }(h, c, t, a, d, o, s, i), b = void 0 !== g[0] && null !== g[0] && "" != g[0], L = void 0 !== g[1] && null !== g[1] && b ? g[1] + g[2] : 0; l += void 0 !== g[0] ? g[0] : ""; u = function (a, t, r, e, s) { if (!e) return ["", 0]; var o = "", i = 0, n = a["a:pPr"], l = B(a, t, r, s), d = l.nodeLaout, p = l.nodeMaster, a = ul(n, ["attrs", "rtl"]); void 0 === a && void 0 === (a = ul(d, ["attrs", "rtl"])) && "shape" != r && (a = ul(p, ["attrs", "rtl"])); t = !1; void 0 !== a && "1" == a && (t = !0); s = ul(n, ["attrs", "algn"]); void 0 === s && void 0 === (s = ul(d, ["attrs", "algn"])) && (s = ul(p, ["attrs", "algn"])); l = ul(n, ["attrs", "indent"]); void 0 === l && void 0 === (l = ul(d, ["attrs", "indent"])) && (l = ul(p, ["attrs", "indent"])); r = 0; void 0 !== l && (r = parseInt(l) * tl); a = ul(n, ["attrs", "marL"]); void 0 === a && void 0 === (a = ul(d, ["attrs", "marL"])) && (a = ul(p, ["attrs", "marL"])); s = 0; void 0 !== a && (s = parseInt(a) * tl); void 0 === l && void 0 === a || (o = t ? "padding-right: " : "padding-left: ", i = e ? Math.abs(0 - r) : Math.abs(s + r), o += i + "px;"); n = ul(n, ["attrs", "marR"]); void 0 === n && void 0 === a && void 0 === (n = ul(d, ["attrs", "marR"])) && (n = ul(p, ["attrs", "marR"])); void 0 !== n && e && (parseInt(n), Math.abs(0 - r)); return [o, i] }(h, o, s, b, i), g = u[0], u = u[1]; void 0 === v && void 0 !== n && 0 != v && (v = n); var m = "", y = 0; if (void 0 === f && void 0 !== h) { var k = P(h, 0, t, a, d, 0, o, s, 0, i); b && (y += (x = ta(k).css({ position: "absolute", float: "left", "white-space": "nowrap", visibility: "hidden" }).appendTo(ta("body"))).outerWidth(), x.remove()), m += k } else if (void 0 !== f) for (var M = 0; M < f.length; M++) { var x, k = P(f[M], 0, h, a, d, 0, o, s, f.length, i); b && (y += (x = ta(k).css({ position: "absolute", float: "left", "white-space": "nowrap", visibility: "hidden" }).appendTo(ta("body"))).outerWidth(), x.remove()), m += k } v = parseInt(v) * tl - L - u, b && y < v && (v = y + L), l += "<div style='height: 100%;direction: initial;overflow-wrap:break-word;word-wrap: break-word;" + (void 0 !== v ? "width:" + v + "px;" : "width:inherit;") + g + "' >", l += m, l += "</div>", l += "</div>" } return l } function B(a, t, r, e) { var s, o, i = 1, a = ul(a["a:pPr"], ["attrs", "lvl"]); return void 0 !== a && (i = parseInt(a) + 1), void 0 !== t && void 0 === (s = ul(e.slideLayoutTables.idxTable[t], ["p:txBody", "a:lstStyle", "a:lvl" + i + "pPr"])) && void 0 === (s = ul(e.slideLayoutTables.idxTable[t], ["p:txBody", "a:p", "a:pPr"])) && (s = ul(e.slideLayoutTables.idxTable[t], ["p:txBody", "a:p", i - 1, "a:pPr"])), void 0 !== r && (o = "a:lvl" + i + "pPr", void 0 === s && (s = ul(e, ["slideLayoutTables", "typeTable", r, "p:txBody", "a:lstStyle", o])), o = ul(e, "title" == r || "ctrTitle" == r ? ["slideMasterTextStyles", "p:titleStyle", o] : "body" == r || "obj" == r || "subTitle" == r ? ["slideMasterTextStyles", "p:bodyStyle", o] : "shape" == r || "diagram" == r ? ["slideMasterTextStyles", "p:otherStyle", o] : "textBox" == r ? ["defaultTextStyle", o] : ["slideMasterTables", "typeTable", r, "p:txBody", "a:lstStyle", o])), { nodeLaout: s, nodeMaster: o } } function P(a, t, r, e, s, o, i, n, l, d) { var p = "", c = e["a:lstStyle"], h = (d.slideMasterTextStyles, a["a:t"]), f = "</sapn>", u = ""; if (void 0 === h && void 0 !== a.type) { if (I) return I = !1, "<sapn class='line-break-br' ></sapn>"; u += "display: block;" } else I = !0; "string" != typeof h && "string" != typeof (h = ul(a, ["a:fld", "a:t"])) && (h = "&nbsp;"); var v = r["a:pPr"], L = 1, g = ul(v, ["attrs", "lvl"]); void 0 !== g && (L = parseInt(g) + 1); var b = B(r, i, n, d), m = b.nodeLaout, y = b.nodeMaster, k = ul(a, ["a:rPr", "attrs", "lang"]), g = void 0 !== k && -1 !== w.indexOf(k), b = ul(v, ["attrs", "rtl"]); void 0 === b && void 0 === (b = ul(m, ["attrs", "rtl"])) && "shape" != n && (b = ul(y, ["attrs", "rtl"])); var M, k = ul(a, ["a:rPr", "a:hlinkClick", "attrs", "r:id"]), v = ""; void 0 !== k && (void 0 !== (v = ul(a, ["a:rPr", "a:hlinkClick", "attrs", "tooltip"])) && (v = "title='" + v + "'"), M = q("a:hlink", void 0, void 0, d), void 0 !== (b = fl(ul(a, ["a:rPr", "a:solidFill"]), void 0, void 0, d)) && "" != b && (M = b)); c = F(a, r, c, s, L, i, n, d), i = c[2]; if ("solid" == i) void 0 === k && void 0 !== c[0] && "" != c[0] ? u += "color: #" + c[0] + ";" : void 0 !== k && void 0 !== M && (u += "color: #" + M + ";"), void 0 !== c[1] && "" != c[1] && ";" != c[1] && (u += "text-shadow:" + c[1] + ";"), void 0 !== c[3] && "" != c[3] && (u += "background-color: #" + c[3] + ";"); else if ("pattern" == i || "pic" == i || "gradient" == i) { if ("pattern" == i) u += "background:" + c[0][0] + ";", null !== c[0][1] && void 0 !== c[0][1] && "" != c[0][1] && (u += "background-size:" + c[0][1] + ";"), null !== c[0][2] && void 0 !== c[0][2] && "" != c[0][2] && (u += "background-position:" + c[0][2] + ";"); else if ("pic" == i) u += c[0] + ";"; else if ("gradient" == i) { var x = c[0].color; u += "background: linear-gradient(" + c[0].rot + "deg,"; for (var P = 0; P < x.length; P++)P == x.length - 1 ? u += "#" + x[P] + ");" : u += "#" + x[P] + ", " } u += "-webkit-background-clip: text;background-clip: text;color: transparent;", void 0 !== c[1].border && "" !== c[1].border && (u += "-webkit-text-stroke: " + c[1].border + ";"), void 0 !== c[1].effcts && "" !== c[1].effcts && (u += "filter: " + c[1].effcts + ";") } p += "font-size:" + N(a, e, 0, L, n, d) + ";font-family:" + function (a, t, r, e) { var s = ul(a, ["a:rPr", "a:latin", "attrs", "typeface"]); void 0 === s && (a = "", void 0 !== e && (a = ul(e, ["attrs", "idx"])), r = ul(r.themeContent, ["a:theme", "a:themeElements", "a:fontScheme"]), "" == a && (a = "title" == t || "subTitle" == t || "ctrTitle" == t ? "major" : "minor"), s = ul(r, ["a:" + a + "Font", "a:latin", "attrs", "typeface"])); return void 0 === s ? "inherit" : s }(a, n, d, s) + ";font-weight:" + (void 0 !== (s = a)["a:rPr"] && "1" === s["a:rPr"].attrs.b ? "bold" : "inherit") + ";font-style:" + (void 0 !== (s = a)["a:rPr"] && "1" === s["a:rPr"].attrs.i ? "italic" : "inherit") + ";text-decoration:" + function (a) { { if (void 0 === a["a:rPr"]) return "inherit"; var t = void 0 !== a["a:rPr"].attrs.u ? a["a:rPr"].attrs.u : "none", a = void 0 !== a["a:rPr"].attrs.strike ? a["a:rPr"].attrs.strike : "noStrike"; return "none" != t && "noStrike" == a ? "underline" : "none" == t && "noStrike" != a ? "line-through" : "none" != t && "noStrike" != a ? "underline line-through" : "inherit" } }(a) + ";text-align:" + function (a, t, r, e) { var s = ul(a, ["a:pPr", "attrs", "algn"]); void 0 === s && (s = ul(t, ["a:pPr", "attrs", "algn"])); void 0 === s && ("title" == r || "ctrTitle" == r || "subTitle" == r ? (a = 1, void 0 !== (t = ul(t, ["a:pPr", "attrs", "lvl"])) && (a = parseInt(t) + 1), void 0 === (s = ul(e, ["slideLayoutTables", "typeTable", r, "p:txBody", "a:lstStyle", a = "a:lvl" + a + "pPr", "attrs", "algn"])) && void 0 === (s = ul(e, ["slideMasterTables", "typeTable", r, "p:txBody", "a:lstStyle", a, "attrs", "algn"])) && void 0 === (s = ul(e, ["slideMasterTextStyles", "p:titleStyle", a, "attrs", "algn"])) && "subTitle" === r && (s = ul(e, ["slideMasterTextStyles", "p:bodyStyle", a, "attrs", "algn"]))) : s = ul(e, "body" == r ? ["slideMasterTextStyles", "p:bodyStyle", "a:lvl1pPr", "attrs", "algn"] : ["slideMasterTables", "typeTable", r, "p:txBody", "a:lstStyle", "a:lvl1pPr", "attrs", "algn"])); var o = "inherit"; if (void 0 !== s) switch (s) { case "l": o = "left"; break; case "r": o = "right"; break; case "ctr": o = "center"; break; case "just": case "dist": o = "justify"; break; default: o = "inherit" }return o }(a, r, n, d) + ";vertical-align:" + function (a) { a = ul(a, ["a:rPr", "attrs", "baseline"]); return void 0 === a ? "baseline" : parseInt(a) / 1e3 + "%" }(a) + ";", u += g ? "direction:rtl;" : "direction:ltr;"; g = ul(a, ["a:rPr", "a:highlight"]); void 0 !== g && (u += "background-color:#" + fl(g, void 0, void 0, d) + ";"); g = ul(a, ["a:rPr", "attrs", "spc"]); void 0 === g && void 0 === (g = ul(m, ["a:defRPr", "attrs", "spc"])) && (g = ul(y, ["a:defRPr", "attrs", "spc"])), void 0 !== g && (u += "letter-spacing: " + parseInt(g) / 100 + "px;"); a = ul(a, ["a:rPr", "attrs", "cap"]); void 0 === a && void 0 === (a = ul(m, ["a:defRPr", "attrs", "cap"])) && (a = ul(y, ["a:defRPr", "attrs", "cap"])), "small" != a && "all" != a || (u += "text-transform: uppercase"); a = ""; u in rl ? a = rl[u].name : (a = "_css_" + (Object.keys(rl).length + 1), rl[u] = { name: a, text: u }); i = "solid" == i && void 0 !== k ? "style='color: inherit;'" : ""; return void 0 === k || "" == k ? "<sapn class='text-block " + a + "' style='" + p + "'>" + h.replace(/\t/g, "&nbsp;&nbsp;&nbsp;&nbsp;").replace(/\s/g, "&nbsp;") + f : "<sapn class='text-block " + a + "' style='" + p + "'><a href='" + Ll(d.slideResObj[k].target) + "' " + i + "  " + v + " target='_blank'>" + h.replace(/\t/g, "&nbsp;&nbsp;&nbsp;&nbsp;").replace(/\s/g, "&nbsp;") + "</a>" + f } function V(a, t, r, e, s, o, i) { var n = ul(a, ["attrs", "rowSpan"]), l = ul(a, ["attrs", "gridSpan"]), d = (ul(a, ["attrs", "vMerge"]), ul(a, ["attrs", "hMerge"]), "word-wrap: break-word;"), p = "", c = "", h = "", f = "", u = "", v = "", L = "", g = parseInt(l), b = 0; if (!isNaN(g) && 1 < g) for (var m = 0; m < g; m++)b += parseInt(ul(t[e + m], ["attrs", "w"])); else b = ul(void 0 === e ? t : t[e], ["attrs", "w"]); var y = ol(a["a:txBody"], a, void 0, 0, void 0, void 0, i, b); 0 != b && (d += "width:" + parseInt(b) * tl + "px;"), void 0 === (f = ul(a, ["a:tcPr", "a:lnB"])) && void 0 !== o && (void 0 !== o && (f = ul(s[o], ["a:tcStyle", "a:tcBdr", "a:bottom", "a:ln"])), void 0 === f && (f = ul(s, ["a:wholeTbl", "a:tcStyle", "a:tcBdr", "a:bottom", "a:ln"]))), void 0 === (u = ul(a, ["a:tcPr", "a:lnT"])) && (void 0 !== o && (u = ul(s[o], ["a:tcStyle", "a:tcBdr", "a:top", "a:ln"])), void 0 === u && (u = ul(s, ["a:wholeTbl", "a:tcStyle", "a:tcBdr", "a:top", "a:ln"]))), void 0 === (v = ul(a, ["a:tcPr", "a:lnL"])) && (void 0 !== o && (v = ul(s[o], ["a:tcStyle", "a:tcBdr", "a:left", "a:ln"])), void 0 === v && (v = ul(s, ["a:wholeTbl", "a:tcStyle", "a:tcBdr", "a:left", "a:ln"]))), void 0 === (L = ul(a, ["a:tcPr", "a:lnR"])) && (void 0 !== o && (L = ul(s[o], ["a:tcStyle", "a:tcBdr", "a:right", "a:ln"])), void 0 === L && (L = ul(s, ["a:wholeTbl", "a:tcStyle", "a:tcBdr", "a:right", "a:ln"]))), ul(a, ["a:tcPr", "a:lnBlToTr"]), ul(a, ["a:tcPr", "a:InTlToBr"]), void 0 === f || "" == f || "" != (f = pl(f, 0, !1, "", i)) && (d += "border-bottom:" + f + ";"), void 0 === u || "" == u || "" != (u = pl(u, 0, !1, "", i)) && (d += "border-top: " + u + ";"), void 0 === v || "" == v || "" != (v = pl(v, 0, !1, "", i)) && (d += "border-left: " + v + ";"), void 0 === L || "" == L || "" != (L = pl(L, 0, !1, "", i)) && (d += "border-right:" + L + ";"); var k, a = ul(a, ["a:tcPr"]); void 0 !== a && "" != a && (p = cl({ "p:spPr": a }, void 0, !1, i, "slide")), "" != p && "background-color: inherit;" != p || (void 0 !== o && (k = ul(s, [o, "a:tcStyle", "a:fill", "a:solidFill"])), void 0 === k || void 0 !== (x = fl(k, void 0, void 0, i)) && (p = " background-color: #" + x + ";")); var M, x = ""; return void 0 !== p && "" != p && (p in rl ? x = rl[p].name : (x = "_tbl_cell_css_" + (Object.keys(rl).length + 1), rl[p] = { name: x, text: p })), void 0 !== o && (M = ul(s, [o, "a:tcTxStyle"])), void 0 !== M && (void 0 !== (i = fl(M, void 0, void 0, i)) && (c = i), (M = "on" == ul(M, ["attrs", "b"]) ? "bold" : "") && (h = M)), d += "" !== c ? "color: #" + c + ";" : "", [y, d += "" != h ? " font-weight:" + h + ";" : "", x, n, l] } function il(a, t, r, e, s) { var o, i, n; void 0 !== a && (o = a["a:off"].attrs), void 0 === o && void 0 !== r ? o = r["a:off"].attrs : void 0 === o && void 0 !== e && (o = e["a:off"].attrs); var l, d = 0, p = 0, a = 0, r = 0; return "group" == s && (e = ul(t, ["p:grpSpPr", "a:xfrm"]), void 0 !== l && (a = parseInt(e["a:off"].attrs.x) * tl, r = parseInt(e["a:off"].attrs.y) * tl)), "group-rotate" == s && void 0 !== t["p:grpSpPr"] && (l = t["p:grpSpPr"]["a:xfrm"], d = parseInt(l["a:chOff"].attrs.x) * tl, p = parseInt(l["a:chOff"].attrs.y) * tl), void 0 === o ? "" : (i = parseInt(o.x) * tl, n = parseInt(o.y) * tl, isNaN(i) || isNaN(n) ? "" : "top:" + (n - p + r) + "px; left:" + (i - d + a) + "px;") } function nl(a, t, r) { var e, s, o = void 0; return void 0 !== a ? o = a["a:ext"].attrs : void 0 !== t ? o = t["a:ext"].attrs : void 0 !== r && (o = r["a:ext"].attrs), void 0 === o ? "" : (e = parseInt(o.cx) * tl, s = parseInt(o.cy) * tl, isNaN(e) || isNaN(s) ? "" : "width:" + e + "px; height:" + s + "px;") } function ll(a, t, r) { a = ul(a, ["p:txBody", "a:bodyPr", "attrs", "anchor"]); return void 0 === a && void 0 === (a = ul(t, ["p:txBody", "a:bodyPr", "attrs", "anchor"])) && void 0 === (a = ul(r, ["p:txBody", "a:bodyPr", "attrs", "anchor"])) && (a = "t"), "ctr" === a ? "v-mid" : "b" === a ? "v-down" : "v-up" } function dl() { return "content" } function F(a, t, r, e, s, o, i, n) { var l, d, p, c = ul(a, ["a:rPr"]), h = "", f = ""; void 0 !== c && ("SOLID_FILL" == (d = hl(c)) ? (l = fl(c["a:solidFill"], void 0, void 0, n), void 0 !== (g = c["a:highlight"]) && (f = fl(g, void 0, void 0, n)), h = "solid") : "PATTERN_FILL" == d ? (l = S(c["a:pattFill"], n), h = "pattern") : "PIC_FILL" == d ? (l = x(c, "slideBg", n, void 0), h = "pic") : "GRADIENT_FILL" == d && (l = _(c["a:gradFill"], n), h = "gradient")), void 0 === l && void 0 !== ul(r, ["a:lvl" + s + "pPr", "a:defRPr"]) && ("SOLID_FILL" == (d = hl(p = ul(r, ["a:lvl" + s + "pPr", "a:defRPr"]))) ? (l = fl(p["a:solidFill"], void 0, void 0, n), void 0 !== (g = p["a:highlight"]) && (f = fl(g, void 0, void 0, n)), h = "solid") : "PATTERN_FILL" == d ? (l = S(p["a:pattFill"], n), h = "pattern") : "PIC_FILL" == d ? (l = x(p, "slideBg", n, void 0), h = "pic") : "GRADIENT_FILL" == d && (l = _(p["a:gradFill"], n), h = "gradient")), void 0 === l && (void 0 !== (p = ul(t, ["p:style", "a:fontRef"])) && (void 0 !== (l = fl(p, void 0, void 0, n)) && (h = "solid"), void 0 !== (g = p["a:highlight"]) && (f = fl(g, void 0, void 0, n))), void 0 === l && void 0 !== e && void 0 !== (l = fl(e, void 0, void 0, n)) && (h = "solid")), void 0 === l && (u = (i = B(t, o, i, n)).nodeLaout, L = i.nodeMaster, void 0 === u || void 0 !== (i = ul(u, ["a:defRPr", "a:solidFill"])) && (l = fl(i, void 0, void 0, n), void 0 !== (g = ul(u, ["a:defRPr", "a:highlight"])) && (f = fl(g, void 0, void 0, n)), h = "solid"), void 0 === l && (void 0 === L || void 0 !== (v = ul(L, ["a:defRPr", "a:solidFill"])) && (l = fl(v, void 0, void 0, n), void 0 !== (g = ul(L, ["a:defRPr", "a:highlight"])) && (f = fl(g, void 0, void 0, n)), h = "solid"))); var u = [], v = {}, L = ul(a, ["a:rPr", "a:ln"]), g = ""; void 0 !== L && void 0 === L["a:noFill"] && (m = pl(a, 0, !1, "text", n).split(" "), b = parseInt(m[0].substring(0, m[0].indexOf("px"))) + "px", m = m[2], "solid" == h ? (g = "-" + b + " 0 " + m + ", 0 " + b + " " + m + ", " + b + " 0 " + m + ", 0 -" + b + " " + m, u.push(g)) : v.border = b + " " + m); var b = ul(a, ["a:rPr", "a:effectLst", "a:glow"]), m = ""; void 0 !== b && (y = fl(b, void 0, void 0, n), m = "0 0 " + (k = b.attrs.rad ? b.attrs.rad * tl : 0) + "px #" + y + ", 0 0 " + k + "px #" + y + ", 0 0 " + k + "px #" + y + ", 0 0 " + k + "px #" + y + ", 0 0 " + k + "px #" + y + ", 0 0 " + k + "px #" + y + ", 0 0 " + k + "px #" + y, "solid" == h ? u.push(m) : u.push("drop-shadow(0 0 " + k / 3 + "px #" + y + ") drop-shadow(0 0 " + 2 * k / 3 + "px #" + y + ") drop-shadow(0 0 " + k + "px #" + y + ")")); var y, m = ul(a, ["a:rPr", "a:effectLst", "a:outerShdw"]), k = ""; void 0 !== m && (M = fl(m, void 0, void 0, n), (y = m.attrs).algn, a = y.dir ? parseInt(y.dir) / 6e4 : 0, n = parseInt(y.dist) * tl, y.rotWithShape, m = y.blurRad ? parseInt(y.blurRad) * tl + "px" : "", y.sx && parseInt(y.sx), y.sy && parseInt(y.sy), y = n * Math.sin(a * Math.PI / 180), a = n * Math.cos(a * Math.PI / 180), isNaN(y) || isNaN(a) || (k = a + "px " + y + "px " + m + " #" + M, "solid" == h ? u.push(k) : u.push("drop-shadow(" + a + "px " + y + "px " + m + " #" + M + ")"))); var M = "", v = "solid" == h ? (0 < u.length && (M = u.join(",")), M + ";") : (0 < u.length && (M = u.join(" ")), v.effcts = M, v); return [l, v, h, f] } function N(a, t, r, e, s, o) { var i, n, l = void 0 !== t ? t["a:lstStyle"] : void 0, d = "a:lvl" + e + "pPr", e = void 0; void 0 !== a["a:rPr"] && (e = parseInt(a["a:rPr"].attrs.sz) / 100), (isNaN(e) || void 0 === e && void 0 !== a["a:fld"]) && (i = ul(a["a:fld"], ["a:rPr", "attrs", "sz"]), e = parseInt(i) / 100), !isNaN(e) && void 0 !== e || void 0 !== a["a:t"] || (i = ul(a["a:endParaRPr"], ["attrs", "sz"]), e = parseInt(i) / 100), !isNaN(e) && void 0 !== e || void 0 === l || (i = ul(l, [d, "a:defRPr", "attrs", "sz"]), e = parseInt(i) / 100); l = !1; void 0 !== t && void 0 !== ul(t, ["a:bodyPr", "a:spAutoFit"]) && (l = !0), !isNaN(e) && void 0 !== e || (i = ul(o.slideLayoutTables, ["typeTable", s, "p:txBody", "a:lstStyle", d, "a:defRPr", "attrs", "sz"]), e = parseInt(i) / 100, n = ul(o.slideLayoutTables, ["typeTable", s, "p:txBody", "a:lstStyle", d, "a:defRPr", "attrs", "kern"]), l && void 0 !== n && !isNaN(e) && 0 < e - parseInt(n) / 100 && (e -= parseInt(n) / 100)), !isNaN(e) && void 0 !== e || (i = ul(o.slideMasterTables, ["typeTable", s, "p:txBody", "a:lstStyle", d, "a:defRPr", "attrs", "sz"]), n = ul(o.slideMasterTables, ["typeTable", s, "p:txBody", "a:lstStyle", d, "a:defRPr", "attrs", "kern"]), void 0 === i && ("title" == s || "subTitle" == s || "ctrTitle" == s ? (i = ul(o.slideMasterTextStyles, ["p:titleStyle", d, "a:defRPr", "attrs", "sz"]), n = ul(o.slideMasterTextStyles, ["p:titleStyle", d, "a:defRPr", "attrs", "kern"])) : "body" == s || "obj" == s || "dt" == s || "sldNum" == s || "textBox" === s ? (i = ul(o.slideMasterTextStyles, ["p:bodyStyle", d, "a:defRPr", "attrs", "sz"]), n = ul(o.slideMasterTextStyles, ["p:bodyStyle", d, "a:defRPr", "attrs", "kern"])) : "shape" == s && (i = ul(o.slideMasterTextStyles, ["p:otherStyle", d, "a:defRPr", "attrs", "sz"]), n = ul(o.slideMasterTextStyles, ["p:otherStyle", d, "a:defRPr", "attrs", "kern"]), l = !1), void 0 === i && (i = ul(o.defaultTextStyle, [d, "a:defRPr", "attrs", "sz"]), n = void 0 === n ? ul(o.defaultTextStyle, [d, "a:defRPr", "attrs", "kern"]) : void 0, l = !1)), e = parseInt(i) / 100, l && void 0 !== n && !isNaN(e) && e - parseInt(n) / 100 > parseInt(n) / 100 && (e -= parseInt(n) / 100)); a = ul(a, ["a:rPr", "attrs", "baseline"]); return void 0 === a || isNaN(e) || (e -= parseInt(a) / 1e5), isNaN(e) || void 0 !== (t = ul(t, ["a:bodyPr", "a:normAutofit", "attrs", "fontScale"])) && 0 != t && (e = Math.round(e * (t / 1e5))), isNaN(e) ? "br" == s ? "initial" : "inherit" : e * R + "px" } function Q(a, t) { var r = ""; return void 0 !== a["a:bottom"] && (r += pl({ "p:spPr": { "a:ln": a["a:bottom"]["a:ln"] } }, 0, !1, "shape", t).replace("border", "border-bottom")), void 0 !== a["a:top"] && (r += pl({ "p:spPr": { "a:ln": a["a:top"]["a:ln"] } }, 0, !1, "shape", t).replace("border", "border-top")), void 0 !== a["a:right"] && (r += pl({ "p:spPr": { "a:ln": a["a:right"]["a:ln"] } }, 0, !1, "shape", t).replace("border", "border-right")), void 0 !== a["a:left"] && (r += pl({ "p:spPr": { "a:ln": a["a:left"]["a:ln"] } }, 0, !1, "shape", t).replace("border", "border-left")), r } function pl(a, t, r, e, s) { var o, i, n, l; if ("shape" == e ? (o = "border: ", i = a["p:spPr"]["a:ln"]) : "text" == e && (o = "", i = a["a:rPr"]["a:ln"]), void 0 !== ul(i, ["a:noFill"])) return "hidden"; if (null == i && void 0 !== (l = ul(a, ["p:style", "a:lnRef"])) && (h = ul(l, ["attrs", "idx"]), i = s.themeContent["a:theme"]["a:themeElements"]["a:fmtScheme"]["a:lnStyleLst"]["a:ln"][Number(h) - 1]), null == i && (o = "", i = a), void 0 !== i) { var d = parseInt(ul(i, ["attrs", "w"])) / 12700; isNaN(d) || d < 1 ? o += 4 / 3 + "px " : o += d + "px "; var p = ul(i, ["a:prstDash", "attrs", "val"]); void 0 === p && (p = ul(i, ["attrs", "cmpd"])); var c = "0"; switch (p) { case "solid": o += "solid", c = "0"; break; case "dash": o += "dashed", c = "5"; break; case "dashDot": o += "dashed", c = "5, 5, 1, 5"; break; case "dot": o += "dotted", c = "1, 5"; break; case "lgDash": o += "dashed", c = "10, 5"; break; case "dbl": o += "double", c = "0"; break; case "lgDashDotDot": o += "dashed", c = "10, 5, 1, 5, 1, 5"; break; case "sysDash": o += "dashed", c = "5, 2"; break; case "sysDashDot": o += "dashed", c = "5, 2, 1, 5"; break; case "sysDashDotDot": o += "dashed", c = "5, 2, 1, 5, 1, 5"; break; case "sysDot": o += "dotted", c = "2, 5"; break; case void 0: default: o += "solid", c = "0" }var h = hl(i); "NO_FILL" == h ? n = r ? "none" : "" : "SOLID_FILL" == h ? n = fl(i["a:solidFill"], void 0, void 0, s) : "GRADIENT_FILL" == h ? n = _(i["a:gradFill"], s) : "PATTERN_FILL" == h && (n = S(i["a:pattFill"], s)) } return void 0 === n && void 0 !== (l = ul(a, ["p:style", "a:lnRef"])) && (n = fl(l, void 0, void 0, s)), o += " " + (n = void 0 === n ? r ? "none" : "hidden" : "#" + n) + " ", r ? { color: n, width: d, type: p, strokeDasharray: c } : o + ";" } function O(a, t) { var r, e, s, o, i, n, l, d, p, c, h, f = a.slideContent, u = a.slideLayoutContent, v = a.slideMasterContent, L = ul(f, ["p:sld", "p:cSld", "p:bg", "p:bgPr"]), g = ul(f, ["p:sld", "p:cSld", "p:bg", "p:bgRef"]); return void 0 !== L ? "SOLID_FILL" == (h = hl(L)) ? r = "background: #" + fl(L["a:solidFill"], s = void 0 !== (e = ul(f, ["p:sld", "p:clrMapOvr", "a:overrideClrMapping", "attrs"])) || void 0 !== (e = ul(u, ["p:sldLayout", "p:clrMapOvr", "a:overrideClrMapping", "attrs"])) ? e : ul(v, ["p:sldMaster", "p:clrMap", "attrs"]), void 0, a) + ";" : "GRADIENT_FILL" == h ? r = M(L, void 0, v, a) : "PIC_FILL" == h && (r = x(L, "slideBg", a, void 0)) : void 0 !== g ? (o = fl(g, s = void 0 !== (e = ul(f, ["p:sld", "p:clrMapOvr", "a:overrideClrMapping", "attrs"])) || void 0 !== (e = ul(u, ["p:sldLayout", "p:clrMapOvr", "a:overrideClrMapping", "attrs"])) ? e : ul(v, ["p:sldMaster", "p:clrMap", "attrs"]), void 0, a), 0 == (i = Number(g.attrs.idx)) || 1e3 == i || 0 < i && i < 1e3 || 1e3 < i && (n = i - 1e3, l = a.themeContent["a:theme"]["a:themeElements"]["a:fmtScheme"]["a:bgFillStyleLst"], d = [], Object.keys(l).forEach(function (a) { var t = l[a]; if ("attrs" != a) if (t.constructor === Array) for (var r, e = 0; e < t.length; e++)(r = {})[a] = t[e], r.idex = t[e].attrs.order, r.attrs = { order: t[e].attrs.order }, d.push(r); else (r = {})[a] = t, r.idex = t.attrs.order, r.attrs = { order: t.attrs.order }, d.push(r) }), (p = d.slice(0)).sort(function (a, t) { return a.idex - t.idex }), "SOLID_FILL" == (h = hl(c = p[n - 1])) ? r = "background: #" + fl(c["a:solidFill"], s, void 0, a) + ";" : "GRADIENT_FILL" == h ? r = M(c, o, v, a) : console.log(h))) : (L = ul(u, ["p:sldLayout", "p:cSld", "p:bg", "p:bgPr"]), g = ul(u, ["p:sldLayout", "p:cSld", "p:bg", "p:bgRef"]), s = void 0 !== (e = ul(u, ["p:sldLayout", "p:clrMapOvr", "a:overrideClrMapping", "attrs"])) ? e : ul(v, ["p:sldMaster", "p:clrMap", "attrs"]), void 0 !== L ? "SOLID_FILL" == (h = hl(L)) ? r = "background: #" + fl(L["a:solidFill"], s, void 0, a) + ";" : "GRADIENT_FILL" == h ? r = M(L, void 0, v, a) : "PIC_FILL" == h && (r = x(L, "slideLayoutBg", a, void 0)) : void 0 !== g ? (console.log("slideLayoutContent: bgRef", g), o = fl(g, s, void 0, a), 0 == (i = Number(g.attrs.idx)) || 1e3 == i || 0 < i && i < 1e3 || 1e3 < i && (n = i - 1e3, l = a.themeContent["a:theme"]["a:themeElements"]["a:fmtScheme"]["a:bgFillStyleLst"], d = [], Object.keys(l).forEach(function (a) { var t = l[a]; if ("attrs" != a) if (t.constructor === Array) for (var r, e = 0; e < t.length; e++)(r = {})[a] = t[e], r.idex = t[e].attrs.order, r.attrs = { order: t[e].attrs.order }, d.push(r); else (r = {})[a] = t, r.idex = t.attrs.order, r.attrs = { order: t.attrs.order }, d.push(r) }), (p = d.slice(0)).sort(function (a, t) { return a.idex - t.idex }), "SOLID_FILL" == (h = hl(c = p[n - 1])) ? r = "background: #" + fl(c["a:solidFill"], s, o, a) + ";" : "GRADIENT_FILL" == h ? r = M(c, o, v, a) : "PIC_FILL" == h ? r = x(c, "themeBg", a, o) : console.log(h))) : (L = ul(v, ["p:sldMaster", "p:cSld", "p:bg", "p:bgPr"]), g = ul(v, ["p:sldMaster", "p:cSld", "p:bg", "p:bgRef"]), s = ul(v, ["p:sldMaster", "p:clrMap", "attrs"]), void 0 !== L ? "SOLID_FILL" == (h = hl(L)) ? r = "background: #" + fl(L["a:solidFill"], s, void 0, a) + ";" : "GRADIENT_FILL" == h ? r = M(L, void 0, v, a) : "PIC_FILL" == h && (r = x(L, "slideMasterBg", a, void 0)) : void 0 !== g && (o = fl(g, s, void 0, a), 0 == (i = Number(g.attrs.idx)) || 1e3 == i || 0 < i && i < 1e3 || 1e3 < i && (n = i - 1e3, l = a.themeContent["a:theme"]["a:themeElements"]["a:fmtScheme"]["a:bgFillStyleLst"], d = [], Object.keys(l).forEach(function (a) { var t = l[a]; if ("attrs" != a) if (t.constructor === Array) for (var r, e = 0; e < t.length; e++)(r = {})[a] = t[e], r.idex = t[e].attrs.order, r.attrs = { order: t[e].attrs.order }, d.push(r); else (r = {})[a] = t, r.idex = t.attrs.order, r.attrs = { order: t.attrs.order }, d.push(r) }), (p = d.slice(0)).sort(function (a, t) { return a.idex - t.idex }), "SOLID_FILL" == (h = hl(c = p[n - 1])) ? r = "background: #" + fl(c["a:solidFill"], s, o, a) + ";" : "GRADIENT_FILL" == h ? r = M(c, o, v, a) : "PIC_FILL" == h ? r = x(c, "themeBg", a, o) : console.log(h))))), r } function M(a, t, r, e) { var s = ""; if (void 0 !== a) { for (var o = a["a:gradFill"], i = o["a:gsLst"]["a:gs"], n = [], l = [], d = 0; d < i.length; d++) { var p = fl(i[d], r["p:sldMaster"]["p:clrMap"].attrs, t, e), c = ul(i[d], ["attrs", "pos"]); l[d] = void 0 !== c ? c / 1e3 + "%" : "", n[d] = "#" + p } a = o["a:lin"], o = 90; void 0 !== a && (o = vl(a.attrs.ang), o += 90), s = "background: linear-gradient(" + o + "deg,"; for (d = 0; d < i.length; d++)d == i.length - 1 ? s += n[d] + " " + l[d] + ");" : s += n[d] + " " + l[d] + ", " } else void 0 !== t && (s = "background: #" + t + ";"); return s } function x(a, t, r, e) { var s, o = C(t, a["a:blipFill"], r), i = a.attrs.order, n = a["a:blipFill"]["a:blip"], l = ul(n, ["a:duotone"]); void 0 !== l && (s = [], Object.keys(l).forEach(function (a) { var t; "attrs" != a && ((t = {})[a] = l[a], s.push(fl(t, void 0, e, r))) })); var d = ul(n, ["a:alphaModFix", "attrs"]), t = ""; void 0 !== d && void 0 !== d.amt && "" != d.amt && (t = "opacity:" + parseInt(d.amt) / 1e5 + ";"); n = ul(a, ["a:blipFill", "a:tile", "attrs"]), d = ""; void 0 !== n && void 0 !== n.sx && (parseInt(n.sx), parseInt(n.sy), parseInt(n.tx), parseInt(n.ty), n.algn, n.flip, d += "background-repeat: round;"); a = ul(a, ["a:blipFill", "a:stretch"]); return void 0 !== a && (d += "background-repeat: no-repeat;", d += "background-position: center;", void 0 !== ul(a, ["a:fillRect", "attrs"]) && (d += "background-size:  100% 100%;;")), "background: url(" + o + ");  z-index: " + i + ";" + d + t } function cl(a, t, r, e, s) { var o, i = hl(ul(a, ["p:spPr"])); if ("NO_FILL" == i) return r ? "none" : ""; if ("SOLID_FILL" == i ? o = fl(a["p:spPr"]["a:solidFill"], void 0, void 0, e) : "GRADIENT_FILL" == i ? o = _(a["p:spPr"]["a:gradFill"], e) : "PATTERN_FILL" == i ? o = S(a["p:spPr"]["a:pattFill"], e) : "PIC_FILL" == i && (o = C(s, a["p:spPr"]["a:blipFill"], e)), void 0 === o) { var n = ul(a, ["p:style", "a:fillRef"]), l = parseInt(ul(a, ["p:style", "a:fillRef", "attrs", "idx"])); if (0 == l || 1e3 == l) return r ? "none" : ""; o = fl(n, void 0, void 0, e) } if (void 0 === o) { n = ul(a, ["p:spPr", "a:grpFill"]); if (void 0 !== n) return cl({ "p:spPr": t["p:grpSpPr"] }, a, r, e, s); if ("NO_FILL" == i) return r ? "none" : "" } if (void 0 === o) return r ? "none" : "background-color: inherit;"; if ("GRADIENT_FILL" == i) { if (r) return o; for (var d = o.color, p = "background: linear-gradient(" + o.rot + "deg,", c = 0; c < d.length; c++)c == d.length - 1 ? p += "#" + d[c] + ");" : p += "#" + d[c] + ", "; return p } if ("PIC_FILL" == i) return r ? o : "background-image:url(" + o + ");"; if ("PATTERN_FILL" != i) return r ? o = tinycolor(o).toRgbString() : "background-color: #" + o + ";"; s = "", i = "", r = "", s = o[0]; return null !== o[1] && void 0 !== o[1] && "" != o[1] && (i = " background-size:" + o[1] + ";"), null !== o[2] && void 0 !== o[2] && "" != o[2] && (r = " background-position:" + o[2] + ";"), "background: " + s + ";" + i + r } function hl(a) { var t = ""; return void 0 !== a["a:noFill"] && (t = "NO_FILL"), void 0 !== a["a:solidFill"] && (t = "SOLID_FILL"), void 0 !== a["a:gradFill"] && (t = "GRADIENT_FILL"), void 0 !== a["a:pattFill"] && (t = "PATTERN_FILL"), void 0 !== a["a:blipFill"] && (t = "PIC_FILL"), void 0 !== a["a:grpFill"] && (t = "GROUP_FILL"), t } function _(a, t) { for (var r = a["a:gsLst"]["a:gs"], e = [], s = 0; s < r.length; s++) { var o = fl(r[s], void 0, void 0, t); e[s] = o } var i = a["a:lin"], a = 0; return void 0 !== i && (a = vl(i.attrs.ang) + 90), { color: e, rot: a } } function C(a, t, r) { var e, s = t["a:blip"].attrs["r:embed"]; if ("slideBg" == a || "slide" == a ? e = ul(r, ["slideResObj", s, "target"]) : "slideLayoutBg" == a ? e = ul(r, ["layoutResObj", s, "target"]) : "slideMasterBg" == a ? e = ul(r, ["masterResObj", s, "target"]) : "themeBg" == a ? e = ul(r, ["themeResObj", s, "target"]) : "diagramBg" == a && (e = ul(r, ["diagramResObj", s, "target"])), void 0 !== e) { if (void 0 === (t = ul(r, ["loaded-images", e]))) { a = (e = Ll(e)).split(".").pop(); if ("xml" == a) return; s = r.zip.file(e).asArrayBuffer(); (function (a, t, r) { if (t.constructor !== Array) throw Error("Error of path type! path is not array."); void 0 !== a && (Object.prototype.set = function (a, t) { for (var r = this, e = a.length, s = 0; s < e; s++) { var o = a[s]; void 0 === r[o] && (r[o] = s == e - 1 ? t : {}), r = r[o] } return r }, a.set(t, r)) })(r, ["loaded-images", e], t = "data:" + X(a) + ";base64," + J(s)) } return t } } function S(a, t) { var r = a["a:bgClr"], e = a["a:fgClr"], s = a.attrs.prst, o = fl(e, void 0, void 0, t); return function (a, t, r) { switch (a) { case "smGrid": return ["linear-gradient(to right,  #" + r + " -1px, transparent 1px ), linear-gradient(to bottom,  #" + r + " -1px, transparent 1px)  #" + t + ";", "4px 4px"]; case "dotGrid": return ["linear-gradient(to right,  #" + r + " -1px, transparent 1px ), linear-gradient(to bottom,  #" + r + " -1px, transparent 1px)  #" + t + ";", "8px 8px"]; case "lgGrid": return ["linear-gradient(to right,  #" + r + " -1px, transparent 1.5px ), linear-gradient(to bottom,  #" + r + " -1px, transparent 1.5px)  #" + t + ";", "8px 8px"]; case "wdUpDiag": return ["repeating-linear-gradient(-45deg, transparent 1px , transparent 4px, #" + r + " 7px)#" + t + ";"]; case "dkUpDiag": return ["repeating-linear-gradient(-45deg, transparent 1px , #" + t + " 5px)#" + r + ";"]; case "ltUpDiag": return ["repeating-linear-gradient(-45deg, transparent 1px , transparent 2px, #" + r + " 4px)#" + t + ";"]; case "wdDnDiag": return ["repeating-linear-gradient(45deg, transparent 1px , transparent 4px, #" + r + " 7px)#" + t + ";"]; case "dkDnDiag": return ["repeating-linear-gradient(45deg, transparent 1px , #" + t + " 5px)#" + r + ";"]; case "ltDnDiag": return ["repeating-linear-gradient(45deg, transparent 1px , transparent 2px, #" + r + " 4px)#" + t + ";"]; case "dkHorz": return ["repeating-linear-gradient(0deg, transparent 1px , transparent 2px, #" + t + " 7px)#" + r + ";"]; case "ltHorz": return ["repeating-linear-gradient(0deg, transparent 1px , transparent 5px, #" + r + " 7px)#" + t + ";"]; case "narHorz": return ["repeating-linear-gradient(0deg, transparent 1px , transparent 2px, #" + r + " 4px)#" + t + ";"]; case "dkVert": return ["repeating-linear-gradient(90deg, transparent 1px , transparent 2px, #" + t + " 7px)#" + r + ";"]; case "ltVert": return ["repeating-linear-gradient(90deg, transparent 1px , transparent 5px, #" + r + " 7px)#" + t + ";"]; case "narVert": return ["repeating-linear-gradient(90deg, transparent 1px , transparent 2px, #" + r + " 4px)#" + t + ";"]; case "lgCheck": case "smCheck": var e = "", s = ""; return s = "lgCheck" == a ? (e = "8px 8px", "0 0, 4px 4px, 4px 4px, 8px 8px") : (e = "4px 4px", "0 0, 2px 2px, 2px 2px, 4px 4px"), ["linear-gradient(45deg,  #" + r + " 25%, transparent 0, transparent 75%,  #" + r + " 0), linear-gradient(45deg,  #" + r + " 25%, transparent 0, transparent 75%,  #" + r + " 0) #" + t + ";", e, s]; case "dashUpDiag": return ["repeating-linear-gradient(152deg, #" + r + ", #" + r + " 5% , transparent 0, transparent 70%)#" + t + ";", "4px 4px"]; case "dashDnDiag": return ["repeating-linear-gradient(45deg, #" + r + ", #" + r + " 5% , transparent 0, transparent 70%)#" + t + ";", "4px 4px"]; case "diagBrick": return ["linear-gradient(45deg, transparent 15%,  #" + r + " 30%, transparent 30%), linear-gradient(-45deg, transparent 15%,  #" + r + " 30%, transparent 30%), linear-gradient(-45deg, transparent 65%,  #" + r + " 80%, transparent 0) #" + t + ";", "4px 4px"]; case "horzBrick": return ["linear-gradient(335deg, #" + t + " 1.6px, transparent 1.6px), linear-gradient(155deg, #" + t + " 1.6px, transparent 1.6px), linear-gradient(335deg, #" + t + " 1.6px, transparent 1.6px), linear-gradient(155deg, #" + t + " 1.6px, transparent 1.6px) #" + r + ";", "4px 4px", "0 0.15px, 0.3px 2.5px, 2px 2.15px, 2.35px 0.4px"]; case "dashVert": return ["linear-gradient(0deg,  #" + t + " 30%, transparent 30%),linear-gradient(90deg,transparent, transparent 40%, #" + r + " 40%, #" + r + " 60% , transparent 60%)#" + t + ";", "4px 4px"]; case "dashHorz": return ["linear-gradient(90deg,  #" + t + " 30%, transparent 30%),linear-gradient(0deg,transparent, transparent 40%, #" + r + " 40%, #" + r + " 60% , transparent 60%)#" + t + ";", "4px 4px"]; case "solidDmnd": return ["linear-gradient(135deg,  #" + r + " 25%, transparent 25%), linear-gradient(225deg,  #" + r + " 25%, transparent 25%), linear-gradient(315deg,  #" + r + " 25%, transparent 25%), linear-gradient(45deg,  #" + r + " 25%, transparent 25%) #" + t + ";", "8px 8px"]; case "openDmnd": return ["linear-gradient(45deg, transparent 0%, transparent calc(50% - 0.5px),  #" + r + " 50%, transparent calc(50% + 0.5px),  transparent 100%), linear-gradient(-45deg, transparent 0%, transparent calc(50% - 0.5px) , #" + r + " 50%, transparent calc(50% + 0.5px),  transparent 100%) #" + t + ";", "8px 8px"]; case "dotDmnd": return ["radial-gradient(#" + r + " 15%, transparent 0), radial-gradient(#" + r + " 15%, transparent 0) #" + t + ";", "4px 4px", "0 0, 2px 2px"]; case "zigZag": case "wave": e = ""; return ["linear-gradient(135deg,  #" + r + " 25%, transparent 25%) 50px " + (e = "zigZag" == a ? "0" : "1px") + ", linear-gradient(225deg,  #" + r + " 25%, transparent 25%) 50px " + e + ", linear-gradient(315deg,  #" + r + " 25%, transparent 25%), linear-gradient(45deg,  #" + r + " 25%, transparent 25%) #" + t + ";", "4px 4px"]; case "lgConfetti": case "smConfetti": e = ""; return ["linear-gradient(135deg,  #" + r + " 25%, transparent 25%) 50px 1px, linear-gradient(225deg,  #" + r + " 25%, transparent 25%), linear-gradient(315deg,  #" + r + " 25%, transparent 25%) 50px 1px , linear-gradient(45deg,  #" + r + " 25%, transparent 25%) #" + t + ";", e = "lgConfetti" == a ? "4px 4px" : "2px 2px"]; case "plaid": return ["linear-gradient(0deg, transparent, transparent 25%, #" + r + "33 25%, #" + r + "33 50%),linear-gradient(90deg, transparent, transparent 25%, #" + r + "66 25%, #" + r + "66 50%) #" + t + ";", "4px 4px"]; case "sphere": return ["radial-gradient(#" + r + " 50%, transparent 50%),#" + t + ";", "4px 4px"]; case "weave": case "shingle": return ["linear-gradient(45deg, #" + t + " 1.31px , #" + r + " 1.4px, #" + r + " 1.5px, transparent 1.5px, transparent 4.2px, #" + r + " 4.2px, #" + r + " 4.3px, transparent 4.31px), linear-gradient(-45deg,  #" + t + " 1.31px , #" + r + " 1.4px, #" + r + " 1.5px, transparent 1.5px, transparent 4.2px, #" + r + " 4.2px, #" + r + " 4.3px, transparent 4.31px) 0 4px, #" + t + ";", "4px 8px"]; case "pct5": case "pct10": case "pct20": case "pct25": case "pct30": case "pct40": case "pct50": case "pct60": case "pct70": case "pct75": case "pct80": case "pct90": case "trellis": case "divot": var o; switch (a) { case "pct5": o = ["0.3px", "10%", "2px 2px"]; break; case "divot": o = ["0.3px", "40%", "4px 4px"]; break; case "pct10": o = ["0.3px", "20%", "2px 2px"]; break; case "pct20": o = ["0.2px", "40%", "2px 2px"]; break; case "pct25": o = ["0.2px", "50%", "2px 2px"]; break; case "pct30": o = ["0.5px", "50%", "2px 2px"]; break; case "pct40": o = ["0.5px", "70%", "2px 2px"]; break; case "pct50": o = ["0.09px", "90%", "2px 2px"]; break; case "pct60": o = ["0.3px", "90%", "2px 2px"]; break; case "pct70": case "trellis": o = ["0.5px", "95%", "2px 2px"]; break; case "pct75": o = ["0.65px", "100%", "2px 2px"]; break; case "pct80": o = ["0.85px", "100%", "2px 2px"]; break; case "pct90": o = ["1px", "100%", "2px 2px"] }return ["radial-gradient(#" + r + " " + o[0] + ", transparent " + o[1] + "),#" + t + ";", o[2]]; default: return [0, 0] } }(s, fl(r, void 0, void 0, t), o) } function fl(a, t, r, e) { if (void 0 !== a) { var s, o = ""; void 0 !== a["a:srgbClr"] ? o = ul(l = a["a:srgbClr"], ["attrs", "val"]) : void 0 !== a["a:schemeClr"] ? o = q("a:" + ul(l = a["a:schemeClr"], ["attrs", "val"]), t, r, e) : void 0 !== a["a:scrgbClr"] ? (t = -1 != (s = (l = a["a:scrgbClr"]).attrs).r.indexOf("%") ? s.r.split("%").shift() : s.r, r = -1 != s.g.indexOf("%") ? s.g.split("%").shift() : s.g, e = -1 != s.b.indexOf("%") ? s.b.split("%").shift() : s.b, o = E(Number(t) / 100 * 255) + E(Number(r) / 100 * 255) + E(Number(e) / 100 * 255)) : void 0 !== a["a:prstClr"] ? o = function (a) { var t, a = ["white", "AliceBlue", "AntiqueWhite", "Aqua", "Aquamarine", "Azure", "Beige", "Bisque", "black", "BlanchedAlmond", "Blue", "BlueViolet", "Brown", "BurlyWood", "CadetBlue", "Chartreuse", "Chocolate", "Coral", "CornflowerBlue", "Cornsilk", "Crimson", "Cyan", "DarkBlue", "DarkCyan", "DarkGoldenRod", "DarkGray", "DarkGrey", "DarkGreen", "DarkKhaki", "DarkMagenta", "DarkOliveGreen", "DarkOrange", "DarkOrchid", "DarkRed", "DarkSalmon", "DarkSeaGreen", "DarkSlateBlue", "DarkSlateGray", "DarkSlateGrey", "DarkTurquoise", "DarkViolet", "DeepPink", "DeepSkyBlue", "DimGray", "DimGrey", "DodgerBlue", "FireBrick", "FloralWhite", "ForestGreen", "Fuchsia", "Gainsboro", "GhostWhite", "Gold", "GoldenRod", "Gray", "Grey", "Green", "GreenYellow", "HoneyDew", "HotPink", "IndianRed", "Indigo", "Ivory", "Khaki", "Lavender", "LavenderBlush", "LawnGreen", "LemonChiffon", "LightBlue", "LightCoral", "LightCyan", "LightGoldenRodYellow", "LightGray", "LightGrey", "LightGreen", "LightPink", "LightSalmon", "LightSeaGreen", "LightSkyBlue", "LightSlateGray", "LightSlateGrey", "LightSteelBlue", "LightYellow", "Lime", "LimeGreen", "Linen", "Magenta", "Maroon", "MediumAquaMarine", "MediumBlue", "MediumOrchid", "MediumPurple", "MediumSeaGreen", "MediumSlateBlue", "MediumSpringGreen", "MediumTurquoise", "MediumVioletRed", "MidnightBlue", "MintCream", "MistyRose", "Moccasin", "NavajoWhite", "Navy", "OldLace", "Olive", "OliveDrab", "Orange", "OrangeRed", "Orchid", "PaleGoldenRod", "PaleGreen", "PaleTurquoise", "PaleVioletRed", "PapayaWhip", "PeachPuff", "Peru", "Pink", "Plum", "PowderBlue", "Purple", "RebeccaPurple", "Red", "RosyBrown", "RoyalBlue", "SaddleBrown", "Salmon", "SandyBrown", "SeaGreen", "SeaShell", "Sienna", "Silver", "SkyBlue", "SlateBlue", "SlateGray", "SlateGrey", "Snow", "SpringGreen", "SteelBlue", "Tan", "Teal", "Thistle", "Tomato", "Turquoise", "Violet", "Wheat", "White", "WhiteSmoke", "Yellow", "YellowGreen"].indexOf(a); -1 != a && (t = ["ffffff", "f0f8ff", "faebd7", "00ffff", "7fffd4", "f0ffff", "f5f5dc", "ffe4c4", "000000", "ffebcd", "0000ff", "8a2be2", "a52a2a", "deb887", "5f9ea0", "7fff00", "d2691e", "ff7f50", "6495ed", "fff8dc", "dc143c", "00ffff", "00008b", "008b8b", "b8860b", "a9a9a9", "a9a9a9", "006400", "bdb76b", "8b008b", "556b2f", "ff8c00", "9932cc", "8b0000", "e9967a", "8fbc8f", "483d8b", "2f4f4f", "2f4f4f", "00ced1", "9400d3", "ff1493", "00bfff", "696969", "696969", "1e90ff", "b22222", "fffaf0", "228b22", "ff00ff", "dcdcdc", "f8f8ff", "ffd700", "daa520", "808080", "808080", "008000", "adff2f", "f0fff0", "ff69b4", "cd5c5c", "4b0082", "fffff0", "f0e68c", "e6e6fa", "fff0f5", "7cfc00", "fffacd", "add8e6", "f08080", "e0ffff", "fafad2", "d3d3d3", "d3d3d3", "90ee90", "ffb6c1", "ffa07a", "20b2aa", "87cefa", "778899", "778899", "b0c4de", "ffffe0", "00ff00", "32cd32", "faf0e6", "ff00ff", "800000", "66cdaa", "0000cd", "ba55d3", "9370db", "3cb371", "7b68ee", "00fa9a", "48d1cc", "c71585", "191970", "f5fffa", "ffe4e1", "ffe4b5", "ffdead", "000080", "fdf5e6", "808000", "6b8e23", "ffa500", "ff4500", "da70d6", "eee8aa", "98fb98", "afeeee", "db7093", "ffefd5", "ffdab9", "cd853f", "ffc0cb", "dda0dd", "b0e0e6", "800080", "663399", "ff0000", "bc8f8f", "4169e1", "8b4513", "fa8072", "f4a460", "2e8b57", "fff5ee", "a0522d", "c0c0c0", "87ceeb", "6a5acd", "708090", "708090", "fffafa", "00ff7f", "4682b4", "d2b48c", "008080", "d8bfd8", "ff6347", "40e0d0", "ee82ee", "f5deb3", "ffffff", "f5f5f5", "ffff00", "9acd32"][a]); return t }(ul(l = a["a:prstClr"], ["attrs", "val"])) : void 0 !== a["a:hslClr"] ? (s = (l = a["a:hslClr"]).attrs, o = E((s = function (a, t, r) { var e, s; s = r <= .5 ? r * (t + 1) : r + t - r * t; return t = 255 * H(e = 2 * r - s, s, (a = a / 60) + 2), r = 255 * H(e, s, a), a = 255 * H(e, s, a - 2), { r: t, g: r, b: a } }(Number(s.hue) / 1e5, Number(-1 != s.sat.indexOf("%") ? s.sat.split("%").shift() : s.sat) / 100, Number(-1 != s.lum.indexOf("%") ? s.lum.split("%").shift() : s.lum) / 100)).r) + E(s.g) + E(s.b)) : void 0 === a["a:sysClr"] || void 0 !== (i = ul(l = a["a:sysClr"], ["attrs", "lastClr"])) && (o = i); var a = !1, i = parseInt(ul(l, ["a:alpha", "attrs", "val"])) / 1e5; isNaN(i) || ((n = tinycolor(o)).setAlpha(i), o = n.toHex8(), a = !0); var n = parseInt(ul(l, ["a:hueMod", "attrs", "val"])) / 1e5; isNaN(n) || (o = function (a, t, r) { a = tinycolor(a).toHsl(), t = a.h * t; 360 <= t && (t -= 360); return r ? tinycolor({ h: cocacl_h, s: a.s, l: a.l, a: a.a }).toHex8() : tinycolor({ h: t, s: a.s, l: a.l, a: a.a }).toHex() }(o, n, a)); n = parseInt(ul(l, ["a:lumMod", "attrs", "val"])) / 1e5; isNaN(n) || (o = function (a, t, r) { a = tinycolor(a).toHsl(), t = a.l * t; 1 <= t && (t = 1); return r ? tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex8() : tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex() }(o, n, a)); n = parseInt(ul(l, ["a:lumOff", "attrs", "val"])) / 1e5; isNaN(n) || (o = function (a, t, r) { a = tinycolor(a).toHsl(), t += a.l; if (1 <= t) return r ? tinycolor({ h: a.h, s: a.s, l: 1, a: a.a }).toHex8() : tinycolor({ h: a.h, s: a.s, l: 1, a: a.a }).toHex(); return r ? tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex8() : tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex() }(o, n, a)); n = parseInt(ul(l, ["a:satMod", "attrs", "val"])) / 1e5; isNaN(n) || (o = function (a, t, r) { a = tinycolor(a).toHsl(), t = a.s * t; 1 <= t && (t = 1); return r ? tinycolor({ h: a.h, s: t, l: a.l, a: a.a }).toHex8() : tinycolor({ h: a.h, s: t, l: a.l, a: a.a }).toHex() }(o, n, a)); n = parseInt(ul(l, ["a:shade", "attrs", "val"])) / 1e5; isNaN(n) || (o = function (a, t, r) { a = tinycolor(a).toHsl(); 1 <= t && (t = 1); t = Math.min(a.l * t, 1); return r ? tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex8() : tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex() }(o, n, a)); var l = parseInt(ul(l, ["a:tint", "attrs", "val"])) / 1e5; return isNaN(l) || (o = function (a, t, r) { a = tinycolor(a).toHsl(); 1 <= t && (t = 1); t = a.l * t + (1 - t); return r ? tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex8() : tinycolor({ h: a.h, s: a.s, l: t, a: a.a }).toHex() }(o, l, a)), o } } function E(a) { for (var t = a.toString(16); t.length < 2;)t = "0" + t; return t } function H(a, t, r) { return r < 0 && (r += 6), 6 <= r && (r -= 6), r < 1 ? (t - a) * r + a : r < 3 ? t : r < 4 ? (t - a) * (4 - r) + a : a } function q(a, t, r, e) { var s; s = void 0 !== t ? t : void 0 !== (t = ul(e.slideContent, ["p:sld", "p:clrMapOvr", "a:overrideClrMapping", "attrs"])) || void 0 !== (t = ul(e.slideLayoutContent, ["p:sldLayout", "p:clrMapOvr", "a:overrideClrMapping", "attrs"])) ? t : ul(e.slideMasterContent, ["p:sldMaster", "p:clrMap", "attrs"]); var o = a.substr(2); if ("phClr" == o && void 0 !== r) i = r; else { if (void 0 !== s) switch (o) { case "tx1": case "tx2": case "bg1": case "bg2": a = "a:" + s[o] } else switch (o) { case "tx1": a = "a:dk1"; break; case "tx2": a = "a:dk2"; break; case "bg1": a = "a:lt1"; break; case "bg2": a = "a:lt2" }var e = ul(e.themeContent, ["a:theme", "a:themeElements", "a:clrScheme", a]), i = ul(e, ["a:srgbClr", "attrs", "val"]); void 0 === i && void 0 !== e && (i = ul(e, ["a:sysClr", "attrs", "lastClr"])) } return i } function U(a) { var r, s = new Array; return void 0 === a || (void 0 !== a["c:xVal"] ? (r = new Array, o(a["c:xVal"]["c:numRef"]["c:numCache"]["c:pt"], function (a, t) { return r.push(parseFloat(a["c:v"])), "" }), s.push(r), r = new Array, o(a["c:yVal"]["c:numRef"]["c:numCache"]["c:pt"], function (a, t) { return r.push(parseFloat(a["c:v"])), "" }), s.push(r)) : o(a, function (a, t) { var r = new Array, t = ul(a, ["c:tx", "c:strRef", "c:strCache", "c:pt", "c:v"]) || t, e = {}; return void 0 !== ul(a, ["c:cat", "c:strRef", "c:strCache", "c:pt"]) ? o(a["c:cat"]["c:strRef"]["c:strCache"]["c:pt"], function (a, t) { return e[a.attrs.idx] = a["c:v"], "" }) : void 0 !== ul(a, ["c:cat", "c:numRef", "c:numCache", "c:pt"]) && o(a["c:cat"]["c:numRef"]["c:numCache"]["c:pt"], function (a, t) { return e[a.attrs.idx] = a["c:v"], "" }), void 0 !== ul(a, ["c:val", "c:numRef", "c:numCache", "c:pt"]) && o(a["c:val"]["c:numRef"]["c:numCache"]["c:pt"], function (a, t) { return r.push({ x: a.attrs.idx, y: parseFloat(a["c:v"]) }), "" }), s.push({ key: t, values: r, xlabels: e }), "" })), s } function ul(a, t) { if (t.constructor !== Array) throw Error("Error of path type! path is not array."); if (void 0 !== a) { for (var r = t.length, e = 0; e < r; e++)if (void 0 === (a = a[t[e]])) return; return a } } function o(a, t) { if (void 0 !== a) { var r = ""; if (a.constructor === Array) for (var e = a.length, s = 0; s < e; s++)r += t(a[s], s); else r += t(a, 0); return r } } function vl(a) { return "" == a || null == a ? 0 : Math.round(a / 6e4) } function X(a) { var t = ""; switch (a.toLowerCase()) { case "jpg": case "jpeg": t = "image/jpeg"; break; case "png": t = "image/png"; break; case "gif": t = "image/gif"; break; case "emf": t = "image/x-emf"; break; case "wmf": t = "image/x-wmf"; break; case "svg": t = "image/svg+xml"; break; case "mp4": t = "video/mp4"; break; case "webm": t = "video/webm"; break; case "ogg": t = "video/ogg"; break; case "avi": t = "video/avi"; break; case "mpg": t = "video/mpg"; break; case "wmv": t = "video/wmv"; break; case "mp3": t = "audio/mpeg"; break; case "wav": t = "audio/wav"; break; case "emf": t = "image/emf"; break; case "wmf": t = "image/wmf"; case "tif": case "tiff": t = "image/tiff" }return t } function Y(a) { for (var t = a, r = 0; r < t.length; r++) { var e = ta(t[r]).find(".numeric-bullet-style"); if (0 < e.length) for (var s = "", o = "", i = 0, n = new Array, l = 0, d = new Array, p = 0; p < e.length; p++) { var c = ta(e[p]).data("bulltname"), h = ta(e[p]).data("bulltlvl"); 0 == i ? (s = c, o = h, n[l] = i, d[l] = c, i++) : c == s && h == o ? (s = c, o = h, i++, n[l] = i, d[l] = c) : c != s && h == o || c != s && Number(h) > Number(o) ? (s = c, o = h, n[++l] = i, d[l] = c, i = 1) : c != s && Number(h) < Number(o) && (s = c, o = h, i = n[--l] + 1); h = function (a, t) { var r = ""; switch (a) { case "arabicPeriod": r = t + ". "; break; case "arabicParenR": r = t + ") "; break; case "alphaLcParenR": r = $(t, "lowerCase") + ") "; break; case "alphaLcPeriod": r = $(t, "lowerCase") + ". "; break; case "alphaUcParenR": r = $(t, "upperCase") + ") "; break; case "alphaUcPeriod": r = $(t, "upperCase") + ". "; break; case "romanUcPeriod": r = W(t) + ". "; break; case "romanLcParenR": r = W(t) + ") "; break; case "hebrew2Minus": r = Z.format(t) + "-"; break; default: r = t }return r }(d[l], i); ta(e[p]).html(h) } } } function W(a) { if (!+a) return !1; for (var t = String(+a).split(""), r = ["", "C", "CC", "CCC", "CD", "D", "DC", "DCC", "DCCC", "CM", "", "X", "XX", "XXX", "XL", "L", "LX", "LXX", "LXXX", "XC", "", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX"], e = "", s = 3; s--;)e = (r[+t.pop() + 10 * s] || "") + e; return Array(+t.join("") + 1).join("M") + e } var Z = function (a) { a.slice().sort(function (a, t) { return t[1].length - a[1].length }); return { format: function (t) { var r = ""; return jQuery.each(a, function () { var a = this[0]; if (0 < parseInt(a)) for (; a <= t; t -= a)r += this[1]; else r = r.replace(a, this[1]) }), r } } }([[1e3, ""], [400, "ת"], [300, "ש"], [200, "ר"], [100, "ק"], [90, "צ"], [80, "פ"], [70, "ע"], [60, "ס"], [50, "נ"], [40, "מ"], [30, "ל"], [20, "כ"], [10, "י"], [9, "ט"], [8, "ח"], [7, "ז"], [6, "ו"], [5, "ה"], [4, "ד"], [3, "ג"], [2, "ב"], [1, "א"], [/יה/, "ט״ו"], [/יו/, "ט״ז"], [/([א-ת])([א-ת])$/, "$1״$2"], [/^([א-ת])$/, "$1׳"]]); function $(a, t) { a = Number(a) - 1; var r = ""; return "upperCase" == t ? r = ((1 <= a / 26 ? String.fromCharCode(a / 26 + 64) : "") + String.fromCharCode(a % 26 + 65)).toUpperCase() : "lowerCase" == t && (r = ((1 <= a / 26 ? String.fromCharCode(a / 26 + 64) : "") + String.fromCharCode(a % 26 + 65)).toLowerCase()), r } function J(a) { for (var t, r = "", e = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", s = new Uint8Array(a), o = s.byteLength, a = o % 3, i = o - a, n = 0; n < i; n += 3)r += e[(16515072 & (t = s[n] << 16 | s[n + 1] << 8 | s[n + 2])) >> 18] + e[(258048 & t) >> 12] + e[(4032 & t) >> 6] + e[63 & t]; return 1 == a ? r += e[(252 & (t = s[i])) >> 2] + e[(3 & t) << 4] + "==" : 2 == a && (r += e[(64512 & (t = s[i] << 8 | s[1 + i])) >> 10] + e[(1008 & t) >> 4] + e[(15 & t) << 2] + "="), r } function K(a) { return a.substr(2 + (~-a.lastIndexOf(".") >>> 0)) } function Ll(a) { var t = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#039;" }; return a.replace(/[&<>"']/g, function (a) { return t[a] }) } function aa(n, a) { "use strict"; function l() { for (var a = []; n[p];)if (n.charCodeAt(p) == s) { if (n.charCodeAt(p + 1) === h) return (p = n.indexOf(o, p)) + 1 && (p += 1), a; if (n.charCodeAt(p + 1) === f) { if (n.charCodeAt(p + 2) == i) { for (; -1 !== p && (n.charCodeAt(p) !== c || n.charCodeAt(p - 1) != i || n.charCodeAt(p - 2) != i || -1 == p);)p = n.indexOf(o, p + 1); -1 === p && (p = n.length) } else for (p += 2; n.charCodeAt(p) !== c && n[p];)p++; p++; continue } var t = r(); a.push(t) } else { t = (t = void 0, t = p, -2 == (p = n.indexOf(e, p) - 1) && (p = n.length), n.slice(t, p + 1)); 0 < t.trim().length && a.push(t), p++ } return a } function d() { for (var a = p; -1 === L.indexOf(n[p]) && n[p];)p++; return n.slice(a, p) } function r() { var a = {}; p++, a.tagName = d(); for (var t, r = !1; n.charCodeAt(p) !== c && n[p];) { var e = n.charCodeAt(p); if (64 < e && e < 91 || 96 < e && e < 123) { for (var s = d(), o = n.charCodeAt(p); o && o !== u && o !== v && !(64 < o && o < 91 || 96 < o && o < 123) && o !== c;)p++, o = n.charCodeAt(p); if (r || (a.attributes = {}, r = !0), o === u || o === v) { var i = (i = e = void 0, e = n[p], i = ++p, p = n.indexOf(e, i), n.slice(i, p)); if (-1 === p) return a } else i = null, p--; a.attributes[s] = i } p++ } return n.charCodeAt(p - 1) !== h ? "script" == a.tagName ? (t = p + 1, p = n.indexOf("<\/script>", p), a.children = [n.slice(t, p - 1)], p += 8) : "style" == a.tagName ? (t = p + 1, p = n.indexOf("</style>", p), a.children = [n.slice(t, p - 1)], p += 7) : -1 == g.indexOf(a.tagName) && (p++, a.children = l()) : p++, a } var t, p = (a = a || {}).pos || 0, e = "<", s = "<".charCodeAt(0), o = ">", c = ">".charCodeAt(0), i = "-".charCodeAt(0), h = "/".charCodeAt(0), f = "!".charCodeAt(0), u = "'".charCodeAt(0), v = '"'.charCodeAt(0), L = "\n\t>/= ", g = ["img", "br", "input", "meta", "link"], b = null; if (void 0 !== a.attrValue) { a.attrName = a.attrName || "id"; for (b = []; -1 !== (t = void 0, t = new RegExp("\\s" + a.attrName + "\\s*=['\"]" + a.attrValue + "['\"]").exec(n), p = t ? t.index : -1);)-1 !== (p = n.lastIndexOf("<", p)) && b.push(r()), n = n.substr(p), p = 0 } else b = (a.parseNode ? r : l)(); return a.filter && (b = aa.filter(b, a.filter)), a.simplify && (b = aa.simplify(b)), b.pos = p, b } e = 1; aa.simplify = function (a) { var t, r = {}; if (void 0 === a) return {}; if (1 === a.length && "string" == typeof a[0]) return a[0]; for (t in a.forEach(function (a) { var t; "object" == typeof a && (r[a.tagName] || (r[a.tagName] = []), t = aa.simplify(a.children || []), r[a.tagName].push(t), a.attributes && (t.attrs = a.attributes), void 0 === t.attrs ? t.attrs = { order: e } : t.attrs.order = e, e++) }), r) 1 == r[t].length && (r[t] = r[t][0]); return r }, aa.filter = function (a, t) { var r = []; return a.forEach(function (a) { "object" == typeof a && t(a) && r.push(a), a.children && (a = aa.filter(a.children, t), r = r.concat(a)) }), r }, aa.stringify = function (a) { function r(a) { if (a) for (var t = 0; t < a.length; t++)"string" == typeof a[t] ? e += a[t].trim() : function (a) { for (var t in e += "<" + a.tagName, a.attributes) e += null === a.attributes[t] ? " " + t : -1 === a.attributes[t].indexOf('"') ? " " + t + '="' + a.attributes[t].trim() + '"' : " " + t + "='" + a.attributes[t].trim() + "'"; e += ">", r(a.children), e += "</" + a.tagName + ">" }(a[t]) } var e = ""; return r(a), e }, aa.toContentString = function (a) { if (Array.isArray(a)) { var t = ""; return a.forEach(function (a) { t = (t += " " + aa.toContentString(a)).trim() }), t } return "object" == typeof a ? aa.toContentString(a.children) : " " + a }, aa.getElementById = function (a, t, r) { t = aa(a, { attrValue: t, simplify: r }); return r ? t : t[0] }, aa.getElementsByClassName = function (a, t, r) { return aa(a, { attrName: "class", attrValue: "[a-zA-Z0-9-s ]*" + t + "[a-zA-Z0-9-s ]*", simplify: r }) }, aa.parseStream = function (e, a) { var t; "function" == typeof a && (cb = a, a = 0), "string" == typeof a && (a = a.length + 2), "string" == typeof e && (t = require("fs"), e = t.createReadStream(e, { start: a }), a = 0); var s = a, o = ""; return e.on("data", function (a) { o += a; for (var t = 0; ;) { s = o.indexOf("<", s) + 1; var r = aa(o, { pos: s, parseNode: !0 }); if ((s = r.pos) > o.length - 1 || s < t) return void (t && (o = o.slice(t), s = 0)); e.emit("xml", r), t = s } o = o.slice(s), s = 0 }), e.on("end", function () { console.log("end") }), e }, "object" == typeof module && (module.exports = aa) }, t = function () { return function e(s, o, i) { function n(r, a) { if (!o[r]) { if (!s[r]) { var t = "function" == typeof require && require; if (!a && t) return t(r, !0); if (l) return l(r, !0); throw new Error("Cannot find module '" + r + "'") } t = o[r] = { exports: {} }; s[r][0].call(t.exports, function (a) { var t = s[r][1][a]; return n(t || a) }, t, t.exports, e, s, o, i) } return o[r].exports } for (var l = "function" == typeof require && require, a = 0; a < i.length; a++)n(i[a]); return n }({ 1: [function (a, t) { "use strict"; function r() { try { return new window.XMLHttpRequest } catch (a) { } } var o = { _getBinaryFromXHR: function (a) { return a.response || a.responseText } }, i = window.ActiveXObject ? function () { return r() || function () { try { return new window.ActiveXObject("Microsoft.XMLHTTP") } catch (a) { } }() } : r; o.getBinaryContent = function (r, e) { try { var s = i(); s.open("GET", r, !0), "responseType" in s && (s.responseType = "arraybuffer"), s.overrideMimeType && s.overrideMimeType("text/plain; charset=x-user-defined"), s.onreadystatechange = function () { var a, t; if (4 === s.readyState) if (200 === s.status || 0 === s.status) { t = a = null; try { a = o._getBinaryFromXHR(s) } catch (a) { t = new Error(a) } e(t, a) } else e(new Error("Ajax error for " + r + " : " + this.status + " " + this.statusText), null) }, s.send() } catch (a) { e(new Error(a), null) } }, t.exports = o }, {}] }, {}, [1])(1) }, "object" == typeof exports ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : "undefined" != typeof window ? window.JSZipUtils = t() : "undefined" != typeof global ? global.JSZipUtils = t() : "undefined" != typeof self && (self.JSZipUtils = t()), function (p) { function c(a, t) { if (t = t || {}, (a = a || "") instanceof c) return a; if (!(this instanceof c)) return new c(a, t); var r, e, s, o, i, n, l, d, s = (e = { r: 0, g: 0, b: 0 }, n = i = o = null, d = l = !(s = 1), "string" == typeof (r = a) && (r = function (a) { a = a.replace(C, "").replace(S, "").toLowerCase(); var t, r = !1; if (F[a]) a = F[a], r = !0; else if ("transparent" == a) return { r: 0, g: 0, b: 0, a: 0, format: "name" }; return (t = O.rgb.exec(a)) ? { r: t[1], g: t[2], b: t[3] } : (t = O.rgba.exec(a)) ? { r: t[1], g: t[2], b: t[3], a: t[4] } : (t = O.hsl.exec(a)) ? { h: t[1], s: t[2], l: t[3] } : (t = O.hsla.exec(a)) ? { h: t[1], s: t[2], l: t[3], a: t[4] } : (t = O.hsv.exec(a)) ? { h: t[1], s: t[2], v: t[3] } : (t = O.hsva.exec(a)) ? { h: t[1], s: t[2], v: t[3], a: t[4] } : (t = O.hex8.exec(a)) ? { r: M(t[1]), g: M(t[2]), b: M(t[3]), a: I(t[4]), format: r ? "name" : "hex8" } : (t = O.hex6.exec(a)) ? { r: M(t[1]), g: M(t[2]), b: M(t[3]), format: r ? "name" : "hex" } : (t = O.hex4.exec(a)) ? { r: M(t[1] + "" + t[1]), g: M(t[2] + "" + t[2]), b: M(t[3] + "" + t[3]), a: I(t[4] + "" + t[4]), format: r ? "name" : "hex8" } : !!(t = O.hex3.exec(a)) && { r: M(t[1] + "" + t[1]), g: M(t[2] + "" + t[2]), b: M(t[3] + "" + t[3]), format: r ? "name" : "hex" } }(r)), "object" == typeof r && (_(r.r) && _(r.g) && _(r.b) ? (e = function (a, t, r) { return { r: 255 * y(a, 255), g: 255 * y(t, 255), b: 255 * y(r, 255) } }(r.r, r.g, r.b), l = !0, d = "%" === String(r.r).substr(-1) ? "prgb" : "rgb") : _(r.h) && _(r.s) && _(r.v) ? (o = P(r.s), i = P(r.v), e = function (a, t, r) { a = 6 * y(a, 360), t = y(t, 100), r = y(r, 100); var e = p.floor(a), s = a - e, o = r * (1 - t), a = r * (1 - s * t), t = r * (1 - (1 - s) * t), e = e % 6; return { r: 255 * [r, a, o, o, t, r][e], g: 255 * [t, r, r, a, o, o][e], b: 255 * [o, o, t, r, r, a][e] } }(r.h, o, i), l = !0, d = "hsv") : _(r.h) && _(r.s) && _(r.l) && (o = P(r.s), n = P(r.l), e = function (a, t, r) { function e(a, t, r) { return r < 0 && (r += 1), 1 < r && --r, r < 1 / 6 ? a + 6 * (t - a) * r : r < .5 ? t : r < 2 / 3 ? a + (t - a) * (2 / 3 - r) * 6 : a } var s, o, i; a = y(a, 360), t = y(t, 100), r = y(r, 100), 0 === t ? s = o = i = r : (s = e(t = 2 * r - (r = r < .5 ? r * (1 + t) : r + t - r * t), r, a + 1 / 3), o = e(t, r, a), i = e(t, r, a - 1 / 3)); return { r: 255 * s, g: 255 * o, b: 255 * i } }(r.h, o, n), l = !0, d = "hsl"), r.hasOwnProperty("a") && (s = r.a)), s = m(s), { ok: l, format: r.format || d, r: D(255, G(e.r, 0)), g: D(255, G(e.g, 0)), b: D(255, G(e.b, 0)), a: s }); this._originalInput = a, this._r = s.r, this._g = s.g, this._b = s.b, this._a = s.a, this._roundA = A(100 * this._a) / 100, this._format = t.format || s.format, this._gradientType = t.gradientType, this._r < 1 && (this._r = A(this._r)), this._g < 1 && (this._g = A(this._g)), this._b < 1 && (this._b = A(this._b)), this._ok = s.ok, this._tc_id = T++ } function e(a, t, r) { a = y(a, 255), t = y(t, 255), r = y(r, 255); var e, s = G(a, t, r), o = D(a, t, r), i = (s + o) / 2; if (s == o) e = l = 0; else { var n = s - o, l = .5 < i ? n / (2 - s - o) : n / (s + o); switch (s) { case a: e = (t - r) / n + (t < r ? 6 : 0); break; case t: e = (r - a) / n + 2; break; case r: e = (a - t) / n + 4 }e /= 6 } return { h: e, s: l, l: i } } function s(a, t, r) { a = y(a, 255), t = y(t, 255), r = y(r, 255); var e, s = G(a, t, r), o = D(a, t, r), i = s, n = s - o, l = 0 === s ? 0 : n / s; if (s == o) e = 0; else { switch (s) { case a: e = (t - r) / n + (t < r ? 6 : 0); break; case t: e = (r - a) / n + 2; break; case r: e = (a - t) / n + 4 }e /= 6 } return { h: e, s: l, v: i } } function t(a, t, r, e) { r = [x(A(a).toString(16)), x(A(t).toString(16)), x(A(r).toString(16))]; return e && r[0].charAt(0) == r[0].charAt(1) && r[1].charAt(0) == r[1].charAt(1) && r[2].charAt(0) == r[2].charAt(1) ? r[0].charAt(0) + r[1].charAt(0) + r[2].charAt(0) : r.join("") } function o(a, t, r, e) { return [x(w(e)), x(A(a).toString(16)), x(A(t).toString(16)), x(A(r).toString(16))].join("") } function a(a, t) { t = 0 === t ? 0 : t || 10; a = c(a).toHsl(); return a.s -= t / 100, a.s = k(a.s), c(a) } function r(a, t) { t = 0 === t ? 0 : t || 10; a = c(a).toHsl(); return a.s += t / 100, a.s = k(a.s), c(a) } function i(a) { return c(a).desaturate(100) } function n(a, t) { t = 0 === t ? 0 : t || 10; a = c(a).toHsl(); return a.l += t / 100, a.l = k(a.l), c(a) } function l(a, t) { t = 0 === t ? 0 : t || 10; a = c(a).toRgb(); return a.r = G(0, D(255, a.r - A(-t / 100 * 255))), a.g = G(0, D(255, a.g - A(-t / 100 * 255))), a.b = G(0, D(255, a.b - A(-t / 100 * 255))), c(a) } function d(a, t) { t = 0 === t ? 0 : t || 10; a = c(a).toHsl(); return a.l -= t / 100, a.l = k(a.l), c(a) } function h(a, t) { a = c(a).toHsl(), t = (a.h + t) % 360; return a.h = t < 0 ? 360 + t : t, c(a) } function f(a) { a = c(a).toHsl(); return a.h = (a.h + 180) % 360, c(a) } function u(a) { var t = c(a).toHsl(), r = t.h; return [c(a), c({ h: (r + 120) % 360, s: t.s, l: t.l }), c({ h: (r + 240) % 360, s: t.s, l: t.l })] } function v(a) { var t = c(a).toHsl(), r = t.h; return [c(a), c({ h: (r + 90) % 360, s: t.s, l: t.l }), c({ h: (r + 180) % 360, s: t.s, l: t.l }), c({ h: (r + 270) % 360, s: t.s, l: t.l })] } function L(a) { var t = c(a).toHsl(), r = t.h; return [c(a), c({ h: (r + 72) % 360, s: t.s, l: t.l }), c({ h: (r + 216) % 360, s: t.s, l: t.l })] } function g(a, t, r) { t = t || 6, r = r || 30; var e = c(a).toHsl(), s = 360 / r, o = [c(a)]; for (e.h = (e.h - (s * t >> 1) + 720) % 360; --t;)e.h = (e.h + s) % 360, o.push(c(e)); return o } function b(a, t) { t = t || 6; for (var a = c(a).toHsv(), r = a.h, e = a.s, s = a.v, o = [], i = 1 / t; t--;)o.push(c({ h: r, s: e, v: s })), s = (s + i) % 1; return o } function m(a) { return a = parseFloat(a), (isNaN(a) || a < 0 || 1 < a) && (a = 1), a } function y(a, t) { "string" == typeof (r = a) && -1 != r.indexOf(".") && 1 === parseFloat(r) && (a = "100%"); var r, r = "string" == typeof (r = a) && -1 != r.indexOf("%"); return a = D(t, G(0, parseFloat(a))), r && (a = parseInt(a * t, 10) / 100), p.abs(a - t) < 1e-6 ? 1 : a % t / parseFloat(t) } function k(a) { return D(1, G(0, a)) } function M(a) { return parseInt(a, 16) } function x(a) { return 1 == a.length ? "0" + a : "" + a } function P(a) { return a <= 1 && (a = 100 * a + "%"), a } function w(a) { return p.round(255 * parseFloat(a)).toString(16) } function I(a) { return M(a) / 255 } function _(a) { return O.CSS_UNIT.exec(a) } var C = /^\s+/, S = /\s+$/, T = 0, A = p.round, D = p.min, G = p.max, j = p.random; c.prototype = { isDark: function () { return this.getBrightness() < 128 }, isLight: function () { return !this.isDark() }, isValid: function () { return this._ok }, getOriginalInput: function () { return this._originalInput }, getFormat: function () { return this._format }, getAlpha: function () { return this._a }, getBrightness: function () { var a = this.toRgb(); return (299 * a.r + 587 * a.g + 114 * a.b) / 1e3 }, getLuminance: function () { var a = this.toRgb(), t = a.r / 255, r = a.g / 255, a = a.b / 255; return .2126 * (t <= .03928 ? t / 12.92 : p.pow((.055 + t) / 1.055, 2.4)) + .7152 * (r <= .03928 ? r / 12.92 : p.pow((.055 + r) / 1.055, 2.4)) + .0722 * (a <= .03928 ? a / 12.92 : p.pow((.055 + a) / 1.055, 2.4)) }, setAlpha: function (a) { return this._a = m(a), this._roundA = A(100 * this._a) / 100, this }, toHsv: function () { var a = s(this._r, this._g, this._b); return { h: 360 * a.h, s: a.s, v: a.v, a: this._a } }, toHsvString: function () { var a = s(this._r, this._g, this._b), t = A(360 * a.h), r = A(100 * a.s), a = A(100 * a.v); return 1 == this._a ? "hsv(" + t + ", " + r + "%, " + a + "%)" : "hsva(" + t + ", " + r + "%, " + a + "%, " + this._roundA + ")" }, toHsl: function () { var a = e(this._r, this._g, this._b); return { h: 360 * a.h, s: a.s, l: a.l, a: this._a } }, toHslString: function () { var a = e(this._r, this._g, this._b), t = A(360 * a.h), r = A(100 * a.s), a = A(100 * a.l); return 1 == this._a ? "hsl(" + t + ", " + r + "%, " + a + "%)" : "hsla(" + t + ", " + r + "%, " + a + "%, " + this._roundA + ")" }, toHex: function (a) { return t(this._r, this._g, this._b, a) }, toHexString: function (a) { return "#" + this.toHex(a) }, toHex8: function (a) { return t = this._r, r = this._g, e = this._b, s = this._a, a = a, s = [x(A(t).toString(16)), x(A(r).toString(16)), x(A(e).toString(16)), x(w(s))], a && s[0].charAt(0) == s[0].charAt(1) && s[1].charAt(0) == s[1].charAt(1) && s[2].charAt(0) == s[2].charAt(1) && s[3].charAt(0) == s[3].charAt(1) ? s[0].charAt(0) + s[1].charAt(0) + s[2].charAt(0) + s[3].charAt(0) : s.join(""); var t, r, e, s }, toHex8String: function (a) { return "#" + this.toHex8(a) }, toRgb: function () { return { r: A(this._r), g: A(this._g), b: A(this._b), a: this._a } }, toRgbString: function () { return 1 == this._a ? "rgb(" + A(this._r) + ", " + A(this._g) + ", " + A(this._b) + ")" : "rgba(" + A(this._r) + ", " + A(this._g) + ", " + A(this._b) + ", " + this._roundA + ")" }, toPercentageRgb: function () { return { r: A(100 * y(this._r, 255)) + "%", g: A(100 * y(this._g, 255)) + "%", b: A(100 * y(this._b, 255)) + "%", a: this._a } }, toPercentageRgbString: function () { return 1 == this._a ? "rgb(" + A(100 * y(this._r, 255)) + "%, " + A(100 * y(this._g, 255)) + "%, " + A(100 * y(this._b, 255)) + "%)" : "rgba(" + A(100 * y(this._r, 255)) + "%, " + A(100 * y(this._g, 255)) + "%, " + A(100 * y(this._b, 255)) + "%, " + this._roundA + ")" }, toName: function () { return 0 === this._a ? "transparent" : !(this._a < 1) && N[t(this._r, this._g, this._b, !0)] || !1 }, toFilter: function (a) { var t = "#" + o(this._r, this._g, this._b, this._a), r = t, e = this._gradientType ? "GradientType = 1, " : ""; return a && (r = "#" + o((a = c(a))._r, a._g, a._b, a._a)), "progid:DXImageTransform.Microsoft.gradient(" + e + "startColorstr=" + t + ",endColorstr=" + r + ")" }, toString: function (a) { var t = !!a; a = a || this._format; var r = !1, e = this._a < 1 && 0 <= this._a; return !t && e && ("hex" === a || "hex6" === a || "hex3" === a || "hex4" === a || "hex8" === a || "name" === a) ? "name" === a && 0 === this._a ? this.toName() : this.toRgbString() : ("rgb" === a && (r = this.toRgbString()), "prgb" === a && (r = this.toPercentageRgbString()), "hex" !== a && "hex6" !== a || (r = this.toHexString()), "hex3" === a && (r = this.toHexString(!0)), "hex4" === a && (r = this.toHex8String(!0)), "hex8" === a && (r = this.toHex8String()), "name" === a && (r = this.toName()), "hsl" === a && (r = this.toHslString()), "hsv" === a && (r = this.toHsvString()), r || this.toHexString()) }, clone: function () { return c(this.toString()) }, _applyModification: function (a, t) { t = a.apply(null, [this].concat([].slice.call(t))); return this._r = t._r, this._g = t._g, this._b = t._b, this.setAlpha(t._a), this }, lighten: function () { return this._applyModification(n, arguments) }, brighten: function () { return this._applyModification(l, arguments) }, darken: function () { return this._applyModification(d, arguments) }, desaturate: function () { return this._applyModification(a, arguments) }, saturate: function () { return this._applyModification(r, arguments) }, greyscale: function () { return this._applyModification(i, arguments) }, spin: function () { return this._applyModification(h, arguments) }, _applyCombination: function (a, t) { return a.apply(null, [this].concat([].slice.call(t))) }, analogous: function () { return this._applyCombination(g, arguments) }, complement: function () { return this._applyCombination(f, arguments) }, monochromatic: function () { return this._applyCombination(b, arguments) }, splitcomplement: function () { return this._applyCombination(L, arguments) }, triad: function () { return this._applyCombination(u, arguments) }, tetrad: function () { return this._applyCombination(v, arguments) } }, c.fromRatio = function (a, t) { if ("object" == typeof a) { var r, e = {}; for (r in a) a.hasOwnProperty(r) && (e[r] = "a" === r ? a[r] : P(a[r])); a = e } return c(a, t) }, c.equals = function (a, t) { return !(!a || !t) && c(a).toRgbString() == c(t).toRgbString() }, c.random = function () { return c.fromRatio({ r: j(), g: j(), b: j() }) }, c.mix = function (a, t, r) { r = 0 === r ? 0 : r || 50; a = c(a).toRgb(), t = c(t).toRgb(), r /= 100; return c({ r: (t.r - a.r) * r + a.r, g: (t.g - a.g) * r + a.g, b: (t.b - a.b) * r + a.b, a: (t.a - a.a) * r + a.a }) }, c.readability = function (a, t) { a = c(a), t = c(t); return (p.max(a.getLuminance(), t.getLuminance()) + .05) / (p.min(a.getLuminance(), t.getLuminance()) + .05) }, c.isReadable = function (a, t, r) { var e = c.readability(a, t), s = !1; switch ("AA" !== (r = ((t = (t = r) || { level: "AA", size: "small" }).level || "AA").toUpperCase()) && "AAA" !== r && (r = "AA"), "small" !== (t = (t.size || "small").toLowerCase()) && "large" !== t && (t = "small"), (t = { level: r, size: t }).level + t.size) { case "AAsmall": case "AAAlarge": s = 4.5 <= e; break; case "AAlarge": s = 3 <= e; break; case "AAAsmall": s = 7 <= e }return s }, c.mostReadable = function (a, t, r) { for (var e, s = null, o = 0, i = (r = r || {}).includeFallbackColors, n = r.level, l = r.size, d = 0; d < t.length; d++)o < (e = c.readability(a, t[d])) && (o = e, s = c(t[d])); return c.isReadable(a, s, { level: n, size: l }) || !i ? s : (r.includeFallbackColors = !1, c.mostReadable(a, ["#fff", "#000"], r)) }; var R, z, B, F = c.names = { aliceblue: "f0f8ff", antiquewhite: "faebd7", aqua: "0ff", aquamarine: "7fffd4", azure: "f0ffff", beige: "f5f5dc", bisque: "ffe4c4", black: "000", blanchedalmond: "ffebcd", blue: "00f", blueviolet: "8a2be2", brown: "a52a2a", burlywood: "deb887", burntsienna: "ea7e5d", cadetblue: "5f9ea0", chartreuse: "7fff00", chocolate: "d2691e", coral: "ff7f50", cornflowerblue: "6495ed", cornsilk: "fff8dc", crimson: "dc143c", cyan: "0ff", darkblue: "00008b", darkcyan: "008b8b", darkgoldenrod: "b8860b", darkgray: "a9a9a9", darkgreen: "006400", darkgrey: "a9a9a9", darkkhaki: "bdb76b", darkmagenta: "8b008b", darkolivegreen: "556b2f", darkorange: "ff8c00", darkorchid: "9932cc", darkred: "8b0000", darksalmon: "e9967a", darkseagreen: "8fbc8f", darkslateblue: "483d8b", darkslategray: "2f4f4f", darkslategrey: "2f4f4f", darkturquoise: "00ced1", darkviolet: "9400d3", deeppink: "ff1493", deepskyblue: "00bfff", dimgray: "696969", dimgrey: "696969", dodgerblue: "1e90ff", firebrick: "b22222", floralwhite: "fffaf0", forestgreen: "228b22", fuchsia: "f0f", gainsboro: "dcdcdc", ghostwhite: "f8f8ff", gold: "ffd700", goldenrod: "daa520", gray: "808080", green: "008000", greenyellow: "adff2f", grey: "808080", honeydew: "f0fff0", hotpink: "ff69b4", indianred: "cd5c5c", indigo: "4b0082", ivory: "fffff0", khaki: "f0e68c", lavender: "e6e6fa", lavenderblush: "fff0f5", lawngreen: "7cfc00", lemonchiffon: "fffacd", lightblue: "add8e6", lightcoral: "f08080", lightcyan: "e0ffff", lightgoldenrodyellow: "fafad2", lightgray: "d3d3d3", lightgreen: "90ee90", lightgrey: "d3d3d3", lightpink: "ffb6c1", lightsalmon: "ffa07a", lightseagreen: "20b2aa", lightskyblue: "87cefa", lightslategray: "789", lightslategrey: "789", lightsteelblue: "b0c4de", lightyellow: "ffffe0", lime: "0f0", limegreen: "32cd32", linen: "faf0e6", magenta: "f0f", maroon: "800000", mediumaquamarine: "66cdaa", mediumblue: "0000cd", mediumorchid: "ba55d3", mediumpurple: "9370db", mediumseagreen: "3cb371", mediumslateblue: "7b68ee", mediumspringgreen: "00fa9a", mediumturquoise: "48d1cc", mediumvioletred: "c71585", midnightblue: "191970", mintcream: "f5fffa", mistyrose: "ffe4e1", moccasin: "ffe4b5", navajowhite: "ffdead", navy: "000080", oldlace: "fdf5e6", olive: "808000", olivedrab: "6b8e23", orange: "ffa500", orangered: "ff4500", orchid: "da70d6", palegoldenrod: "eee8aa", palegreen: "98fb98", paleturquoise: "afeeee", palevioletred: "db7093", papayawhip: "ffefd5", peachpuff: "ffdab9", peru: "cd853f", pink: "ffc0cb", plum: "dda0dd", powderblue: "b0e0e6", purple: "800080", rebeccapurple: "663399", red: "f00", rosybrown: "bc8f8f", royalblue: "4169e1", saddlebrown: "8b4513", salmon: "fa8072", sandybrown: "f4a460", seagreen: "2e8b57", seashell: "fff5ee", sienna: "a0522d", silver: "c0c0c0", skyblue: "87ceeb", slateblue: "6a5acd", slategray: "708090", slategrey: "708090", snow: "fffafa", springgreen: "00ff7f", steelblue: "4682b4", tan: "d2b48c", teal: "008080", thistle: "d8bfd8", tomato: "ff6347", turquoise: "40e0d0", violet: "ee82ee", wheat: "f5deb3", white: "fff", whitesmoke: "f5f5f5", yellow: "ff0", yellowgreen: "9acd32" }, N = c.hexNames = function (a) { var t, r = {}; for (t in a) a.hasOwnProperty(t) && (r[a[t]] = t); return r }(F), O = (z = "[\\s|\\(]+(" + (R = "(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)") + ")[,|\\s]+(" + R + ")[,|\\s]+(" + R + ")\\s*\\)?", B = "[\\s|\\(]+(" + R + ")[,|\\s]+(" + R + ")[,|\\s]+(" + R + ")[,|\\s]+(" + R + ")\\s*\\)?", { CSS_UNIT: new RegExp(R), rgb: new RegExp("rgb" + z), rgba: new RegExp("rgba" + B), hsl: new RegExp("hsl" + z), hsla: new RegExp("hsla" + B), hsv: new RegExp("hsv" + z), hsva: new RegExp("hsva" + B), hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/, hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/, hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/, hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/ }); "undefined" != typeof module && module.exports ? module.exports = c : "function" == typeof define && define.amd ? define(function () { return c }) : window.tinycolor = c }(Math) }(jQuery);